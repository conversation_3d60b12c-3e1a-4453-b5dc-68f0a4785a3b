#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件分析器 - 尝试分析Excel文件结构
"""

import zipfile
import xml.etree.ElementTree as ET
import re

def get_shared_strings(zip_file):
    """获取共享字符串"""
    shared_strings = []
    try:
        with zip_file.open('xl/sharedStrings.xml') as f:
            content = f.read().decode('utf-8')
            # 更精确的解析
            root = ET.fromstring(content)
            for si in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si'):
                t = si.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                if t is not None:
                    shared_strings.append(t.text or "")
                else:
                    shared_strings.append("")
    except Exception as e:
        print(f"读取共享字符串失败: {e}")
        # 备用方法
        try:
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                strings = re.findall(r'<t[^>]*>([^<]*)</t>', content)
                shared_strings = strings
        except:
            pass
    return shared_strings

def parse_row_data(row_xml, shared_strings):
    """解析行数据"""
    cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_xml)
    row_data = []

    for cell_ref, cell_type, value in cells:
        if cell_type == 's' and value:  # 共享字符串
            try:
                idx = int(value)
                if 0 <= idx < len(shared_strings):
                    row_data.append(shared_strings[idx])
                else:
                    row_data.append(f"[字符串索引{idx}]")
            except:
                row_data.append(value)
        elif value:
            row_data.append(value)
        else:
            row_data.append("")

    return row_data

def identify_columns(filename):
    """识别关键列"""
    print(f"\n识别 {filename} 中的关键列:")
    print("-" * 30)

    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            shared_strings = get_shared_strings(zip_file)
            print(f"共享字符串数量: {len(shared_strings)}")

            # 读取工作表数据
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*>(.*?)</row>', content, re.DOTALL)

                # 确定表头行
                header_row_idx = 3 if filename.upper().endswith('A.XLSX') else 0

                print(f"总行数: {len(rows)}, 表头行索引: {header_row_idx}")

                if header_row_idx < len(rows):
                    header_row = rows[header_row_idx]
                    headers = parse_row_data(header_row, shared_strings)

                    print("列名:")
                    for i, header in enumerate(headers):
                        print(f"{i}: {header}")

                    # 识别关键列
                    if filename.upper().endswith('A.XLSX'):
                        print("\n关键列识别:")
                        for i, header in enumerate(headers):
                            if '承包方代表证件号码' in str(header):
                                print(f"身份证号码列: {i} ({header})")
                            elif '地址' in str(header):
                                print(f"地址列: {i} ({header})")
                            elif '原合同面积' in str(header):
                                print(f"原合同面积列: {i} ({header})")
                            elif '确权' in str(header) and '面积' in str(header):
                                print(f"确权面积列: {i} ({header})")
                            elif '姓名' in str(header) or '承包方' in str(header):
                                print(f"姓名列: {i} ({header})")

                    elif filename.upper().endswith('B.XLSX'):
                        print("\n关键列识别:")
                        for i, header in enumerate(headers):
                            if '权利人名称' in str(header):
                                print(f"权利人名称列: {i} ({header})")
                            elif '坐落' in str(header):
                                print(f"坐落列: {i} ({header})")

                    return headers

    except Exception as e:
        print(f"识别列失败: {e}")
        return []

def sample_data_analysis(filename, headers):
    """样本数据分析"""
    print(f"\n{filename} 样本数据分析:")
    print("-" * 30)

    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            shared_strings = get_shared_strings(zip_file)

            # 读取工作表数据
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*>(.*?)</row>', content, re.DOTALL)

                # 确定数据开始行
                start_row = 4 if filename.upper().endswith('A.XLSX') else 1

                print(f"数据行数: {len(rows) - start_row}")

                # 分析前几行数据
                sample_count = min(5, len(rows) - start_row)
                print(f"\n前{sample_count}行数据样本:")

                for i in range(start_row, start_row + sample_count):
                    if i < len(rows):
                        row = rows[i]
                        row_data = parse_row_data(row, shared_strings)

                        print(f"数据行{i-start_row+1}:")
                        for j, (header, data) in enumerate(zip(headers, row_data)):
                            if j < 10:  # 显示前10列
                                print(f"  {header}: {data}")
                        print()

    except Exception as e:
        print(f"样本数据分析失败: {e}")

def main():
    """主函数"""
    print("Excel文件结构分析器")
    print("=" * 50)
    
    # 分析A表
    print("\n正在分析A表...")
    try:
        headers_a = identify_columns('A.XLSX')
        sample_data_analysis('A.XLSX', headers_a)
    except Exception as e:
        print(f"分析A表失败: {e}")
    
    # 分析B表
    print("\n正在分析B表...")
    try:
        headers_b = identify_columns('B.xlsx')
        sample_data_analysis('B.xlsx', headers_b)
    except Exception as e:
        print(f"分析B表失败: {e}")
    
    print("\n分析完成！")
    print("基于以上分析，您可以:")
    print("1. 查看数据结构和质量")
    print("2. 确认关键列的位置")
    print("3. 评估数据匹配的可行性")

if __name__ == "__main__":
    main()
