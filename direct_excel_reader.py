#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接读取Excel文件内容
"""

import zipfile
import xml.etree.ElementTree as ET
import re

def extract_shared_strings(filename):
    """提取共享字符串"""
    print(f"提取 {filename} 的共享字符串...")
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                
                # 使用XML解析
                try:
                    root = ET.fromstring(content)
                    strings = []
                    for si in root:
                        text = ""
                        for child in si:
                            if child.text:
                                text += child.text
                        strings.append(text)
                    
                    print(f"成功解析 {len(strings)} 个共享字符串")
                    return strings
                except Exception as e:
                    print(f"XML解析失败: {e}")
                    
                # 备用正则表达式方法
                strings = re.findall(r'<t[^>]*>([^<]*)</t>', content)
                print(f"正则表达式解析得到 {len(strings)} 个字符串")
                return strings
                
    except Exception as e:
        print(f"提取共享字符串失败: {e}")
        return []

def show_sample_strings(strings, count=50):
    """显示样本字符串"""
    print(f"\n前{min(count, len(strings))}个共享字符串:")
    for i, s in enumerate(strings[:count]):
        print(f"{i}: {s}")

def analyze_worksheet_structure(filename):
    """分析工作表结构"""
    print(f"\n分析 {filename} 的工作表结构...")
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            # 先获取共享字符串
            shared_strings = extract_shared_strings(filename)
            
            # 读取工作表
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                
                # 查找所有行
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                print(f"找到 {len(rows)} 行数据")
                
                # 分析前几行
                for row_num, row_content in rows[:10]:
                    print(f"\n行 {row_num}:")
                    
                    # 查找单元格
                    cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_content)
                    
                    for cell_ref, cell_type, value in cells:
                        if cell_type == 's' and value:  # 共享字符串
                            try:
                                idx = int(value)
                                if 0 <= idx < len(shared_strings):
                                    actual_value = shared_strings[idx]
                                    print(f"  {cell_ref}: {actual_value} (字符串索引{idx})")
                                else:
                                    print(f"  {cell_ref}: [索引{idx}超出范围]")
                            except:
                                print(f"  {cell_ref}: {value} (无法解析索引)")
                        elif value:
                            print(f"  {cell_ref}: {value}")
                        else:
                            print(f"  {cell_ref}: [空]")
                    
                    # 如果是A表第4行或B表第1行，标记为表头
                    if (filename.upper().endswith('A.XLSX') and row_num == '4') or \
                       (filename.upper().endswith('B.XLSX') and row_num == '1'):
                        print("  *** 这是表头行 ***")
                
    except Exception as e:
        print(f"分析工作表结构失败: {e}")

def generate_data_report(filename):
    """生成数据报告"""
    print(f"\n生成 {filename} 的数据报告...")
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            shared_strings = extract_shared_strings(filename)
            
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                # 确定表头行和数据开始行
                if filename.upper().endswith('A.XLSX'):
                    header_row_num = 4
                    data_start_row = 5
                    print("A表：表头在第4行，数据从第5行开始")
                else:
                    header_row_num = 1
                    data_start_row = 2
                    print("B表：表头在第1行，数据从第2行开始")
                
                # 提取表头
                headers = []
                for row_num, row_content in rows:
                    if int(row_num) == header_row_num:
                        cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_content)
                        for cell_ref, cell_type, value in cells:
                            if cell_type == 's' and value:
                                try:
                                    idx = int(value)
                                    if 0 <= idx < len(shared_strings):
                                        headers.append(shared_strings[idx])
                                    else:
                                        headers.append(f"[索引{idx}]")
                                except:
                                    headers.append(value)
                            else:
                                headers.append(value or "")
                        break
                
                print(f"\n表头列名 ({len(headers)}列):")
                for i, header in enumerate(headers):
                    print(f"  {i}: {header}")
                
                # 统计数据行数
                data_rows = [r for r in rows if int(r[0]) >= data_start_row]
                print(f"\n数据行数: {len(data_rows)}")
                
                # 分析关键列
                if filename.upper().endswith('A.XLSX'):
                    print("\nA表关键列识别:")
                    for i, header in enumerate(headers):
                        if '承包方代表证件号码' in header:
                            print(f"  身份证号码列: {i} - {header}")
                        elif '地址' in header:
                            print(f"  地址列: {i} - {header}")
                        elif '原合同面积' in header:
                            print(f"  原合同面积列: {i} - {header}")
                        elif '确权' in header and '面积' in header:
                            print(f"  确权面积列: {i} - {header}")
                        elif '姓名' in header or '承包方' in header:
                            print(f"  姓名列: {i} - {header}")
                
                elif filename.upper().endswith('B.XLSX'):
                    print("\nB表关键列识别:")
                    for i, header in enumerate(headers):
                        if '权利人名称' in header:
                            print(f"  权利人名称列: {i} - {header}")
                        elif '坐落' in header:
                            print(f"  坐落列: {i} - {header}")
                
                return headers, data_rows
                
    except Exception as e:
        print(f"生成数据报告失败: {e}")
        return [], []

def main():
    """主函数"""
    print("Excel文件直接读取器")
    print("=" * 50)
    
    # 分析A表
    print("\n" + "="*50)
    print("分析A表")
    print("="*50)
    headers_a, data_a = generate_data_report('A.XLSX')
    
    # 分析B表
    print("\n" + "="*50)
    print("分析B表")
    print("="*50)
    headers_b, data_b = generate_data_report('B.xlsx')
    
    # 生成合并可行性报告
    print("\n" + "="*50)
    print("合并可行性分析")
    print("="*50)
    
    print(f"A表数据量: {len(data_a)} 行")
    print(f"B表数据量: {len(data_b)} 行")
    
    # 检查关键列是否存在
    a_has_name = any('姓名' in str(h) or '承包方' in str(h) for h in headers_a)
    a_has_address = any('地址' in str(h) for h in headers_a)
    a_has_id = any('承包方代表证件号码' in str(h) for h in headers_a)
    
    b_has_name = any('权利人名称' in str(h) for h in headers_b)
    b_has_address = any('坐落' in str(h) for h in headers_b)
    
    print(f"\nA表关键列检查:")
    print(f"  姓名列: {'✓' if a_has_name else '✗'}")
    print(f"  地址列: {'✓' if a_has_address else '✗'}")
    print(f"  身份证列: {'✓' if a_has_id else '✗'}")
    
    print(f"\nB表关键列检查:")
    print(f"  权利人名称列: {'✓' if b_has_name else '✗'}")
    print(f"  坐落列: {'✓' if b_has_address else '✗'}")
    
    if a_has_name and a_has_address and b_has_name and b_has_address:
        print("\n✓ 数据合并条件满足，可以进行匹配")
    else:
        print("\n✗ 缺少必要的匹配列，需要手动确认列名")

if __name__ == "__main__":
    main()
