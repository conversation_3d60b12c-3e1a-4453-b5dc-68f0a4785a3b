#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单Excel文件读取器 - 直接查看XML内容
"""

import zipfile
import re

def extract_and_show_strings(filename):
    """提取并显示共享字符串"""
    print(f"\n分析 {filename} 的共享字符串...")
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                
                # 显示原始XML的一部分
                print("原始XML内容片段:")
                print(content[:1000] + "...")
                
                # 尝试多种方法提取字符串
                print("\n方法1 - 简单正则:")
                strings1 = re.findall(r'<t[^>]*>([^<]*)</t>', content)
                print(f"找到 {len(strings1)} 个字符串")
                if strings1:
                    print("前20个:")
                    for i, s in enumerate(strings1[:20]):
                        print(f"  {i}: {s}")
                
                print("\n方法2 - 包含CDATA:")
                strings2 = re.findall(r'<t[^>]*>(?:<!\[CDATA\[)?([^<]*?)(?:\]\]>)?</t>', content)
                print(f"找到 {len(strings2)} 个字符串")
                if strings2:
                    print("前20个:")
                    for i, s in enumerate(strings2[:20]):
                        print(f"  {i}: {s}")
                
                print("\n方法3 - 所有文本内容:")
                # 查找所有<si>元素内的文本
                si_elements = re.findall(r'<si[^>]*>(.*?)</si>', content, re.DOTALL)
                strings3 = []
                for si in si_elements:
                    # 从每个si元素中提取文本
                    texts = re.findall(r'>([^<]+)<', si)
                    if texts:
                        strings3.append(''.join(texts))
                    else:
                        strings3.append("")
                
                print(f"找到 {len(strings3)} 个字符串")
                if strings3:
                    print("前20个:")
                    for i, s in enumerate(strings3[:20]):
                        print(f"  {i}: {s}")
                
                return strings3 if strings3 else strings1
                
    except Exception as e:
        print(f"提取失败: {e}")
        return []

def analyze_worksheet_with_strings(filename, shared_strings):
    """使用共享字符串分析工作表"""
    print(f"\n分析 {filename} 的工作表...")
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                
                # 查找行
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                # 确定要分析的行
                if filename.upper().endswith('A.XLSX'):
                    target_rows = [4, 5, 6]  # 表头和前两行数据
                    print("A表 - 分析第4行(表头)和第5-6行(数据)")
                else:
                    target_rows = [1, 2, 3]  # 表头和前两行数据
                    print("B表 - 分析第1行(表头)和第2-3行(数据)")
                
                for target_row in target_rows:
                    for row_num, row_content in rows:
                        if int(row_num) == target_row:
                            print(f"\n第{row_num}行:")
                            
                            # 查找单元格
                            cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_content)
                            
                            row_data = []
                            for cell_ref, cell_type, value in cells:
                                if cell_type == 's' and value:  # 共享字符串
                                    try:
                                        idx = int(value)
                                        if 0 <= idx < len(shared_strings):
                                            actual_value = shared_strings[idx]
                                            row_data.append(actual_value)
                                            print(f"  {cell_ref}: {actual_value}")
                                        else:
                                            row_data.append(f"[索引{idx}超出范围]")
                                            print(f"  {cell_ref}: [索引{idx}超出范围]")
                                    except:
                                        row_data.append(value)
                                        print(f"  {cell_ref}: {value} (无法解析)")
                                elif value:
                                    row_data.append(value)
                                    print(f"  {cell_ref}: {value}")
                                else:
                                    row_data.append("")
                                    print(f"  {cell_ref}: [空]")
                            
                            if target_row == (4 if filename.upper().endswith('A.XLSX') else 1):
                                print(f"  *** 表头行，共{len(row_data)}列 ***")
                                return row_data
                            break
                
    except Exception as e:
        print(f"分析工作表失败: {e}")
        return []

def main():
    """主函数"""
    print("简单Excel文件读取器")
    print("=" * 60)
    
    # 分析A表
    print("\n" + "="*60)
    print("分析A表")
    print("="*60)
    
    strings_a = extract_and_show_strings('A.XLSX')
    headers_a = analyze_worksheet_with_strings('A.XLSX', strings_a)
    
    # 分析B表
    print("\n" + "="*60)
    print("分析B表")
    print("="*60)
    
    strings_b = extract_and_show_strings('B.xlsx')
    headers_b = analyze_worksheet_with_strings('B.xlsx', strings_b)
    
    # 总结
    print("\n" + "="*60)
    print("分析总结")
    print("="*60)
    
    print(f"A表表头 ({len(headers_a)}列):")
    for i, header in enumerate(headers_a):
        print(f"  {i}: {header}")
    
    print(f"\nB表表头 ({len(headers_b)}列):")
    for i, header in enumerate(headers_b):
        print(f"  {i}: {header}")
    
    # 查找关键列
    print("\n关键列识别:")
    print("A表:")
    for i, header in enumerate(headers_a):
        if '承包方代表证件号码' in str(header):
            print(f"  身份证号码列: {i} - {header}")
        elif '地址' in str(header):
            print(f"  地址列: {i} - {header}")
        elif '原合同面积' in str(header):
            print(f"  原合同面积列: {i} - {header}")
        elif '确权' in str(header) and '面积' in str(header):
            print(f"  确权面积列: {i} - {header}")
        elif '姓名' in str(header) or '承包方' in str(header):
            print(f"  姓名列: {i} - {header}")
    
    print("B表:")
    for i, header in enumerate(headers_b):
        if '权利人名称' in str(header):
            print(f"  权利人名称列: {i} - {header}")
        elif '坐落' in str(header):
            print(f"  坐落列: {i} - {header}")

if __name__ == "__main__":
    main()
