#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终智能数据合并脚本 - 统一使用B表列名
"""

import zipfile
import re
import csv
from difflib import SequenceMatcher
from collections import defaultdict

def extract_shared_strings(filename):
    """提取共享字符串"""
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                strings = re.findall(r'<t[^>]*>([^<]*)</t>', content)
                return strings
    except Exception as e:
        print(f"提取共享字符串失败: {e}")
        return []

def parse_row_correct(row_xml, shared_strings):
    """正确解析行数据"""
    cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_xml)
    row_data = {}
    
    for cell_ref, cell_type, value in cells:
        col_letter = ''.join(filter(str.isalpha, cell_ref))
        col_index = sum((ord(c) - ord('A') + 1) * (26 ** i) for i, c in enumerate(reversed(col_letter))) - 1
        
        if value:
            try:
                idx = int(value)
                if 0 <= idx < len(shared_strings):
                    row_data[col_index] = shared_strings[idx]
                else:
                    row_data[col_index] = value
            except ValueError:
                row_data[col_index] = value
        else:
            row_data[col_index] = ""
    
    return row_data

def read_excel_data(filename, header_row, data_start_row):
    """读取Excel数据"""
    print(f"读取 {filename}...")
    
    shared_strings = extract_shared_strings(filename)
    print(f"共享字符串数量: {len(shared_strings)}")
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                # 提取表头
                headers = {}
                for row_num, row_content in rows:
                    if int(row_num) == header_row:
                        headers = parse_row_correct(row_content, shared_strings)
                        break
                
                # 提取所有数据
                data = []
                for row_num, row_content in rows:
                    if int(row_num) >= data_start_row:
                        row_data = parse_row_correct(row_content, shared_strings)
                        max_col = max(headers.keys()) if headers else 0
                        for i in range(max_col + 1):
                            if i not in row_data:
                                row_data[i] = ""
                        data.append(row_data)
                
                print(f"表头: {len(headers)}列, 数据: {len(data)}行")
                return headers, data
                
    except Exception as e:
        print(f"读取失败: {e}")
        return {}, []

def similarity(a, b):
    """计算字符串相似度"""
    if not a or not b:
        return 0
    return SequenceMatcher(None, str(a), str(b)).ratio()

def extract_village_name(address):
    """从地址中提取村名"""
    if not address:
        return ""
    
    address = str(address)
    patterns = [
        r'([^市县区镇乡街道]*村)(?:委员会)?',
        r'([^市县区镇乡街道]*社区)',
        r'([^市县区镇乡街道]*组)',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, address)
        if matches:
            return matches[-1]
    
    town_patterns = [
        r'([^市县区]*镇)',
        r'([^市县区]*乡)',
    ]
    
    for pattern in town_patterns:
        matches = re.findall(pattern, address)
        if matches:
            return matches[-1]
    
    return address

def clean_name(name):
    """清理姓名"""
    if not name:
        return ""
    return str(name).strip().replace(' ', '').replace('　', '')

def has_multiple_people_or_company(name):
    """检查是否为多人或公司"""
    if not name:
        return False
    
    name_str = str(name)
    if '、' in name_str or '，' in name_str or ',' in name_str:
        return True
    
    company_keywords = ['公司', '企业', '集团', '有限', '股份', '合作社', '协会', '基金会', '中心', '组织']
    return any(keyword in name_str for keyword in company_keywords)

def smart_matching(a_data, b_data):
    """智能匹配策略：姓名优先，村名辅助"""
    print(f"\n执行智能匹配...")
    
    # 列索引
    A_NAME_COL = 1      # 承包方代表名称
    A_ADDRESS_COL = 13  # 地址
    B_NAME_COL = 8      # 权利人名称
    B_ADDRESS_COL = 1   # 坐落
    
    # 建立姓名索引
    print("建立姓名索引...")
    a_name_index = defaultdict(list)
    for a_idx, a_row in enumerate(a_data):
        name = clean_name(a_row.get(A_NAME_COL, ""))
        if name:
            a_name_index[name].append(a_idx)
    
    print(f"A表姓名索引：{len(a_name_index)}个不同姓名")
    duplicate_names = {name: indices for name, indices in a_name_index.items() if len(indices) > 1}
    print(f"A表重名情况：{len(duplicate_names)}个姓名有重复")
    
    # 匹配处理
    matches = []
    match_details = []
    matched_a_indices = set()
    
    stats = {
        'total_b': 0,
        'skipped_multi': 0,
        'exact_match': 0,
        'village_match': 0,
        'no_match': 0,
        'multiple_candidates': 0
    }
    
    for b_idx, b_row in enumerate(b_data):
        stats['total_b'] += 1
        
        b_name = clean_name(b_row.get(B_NAME_COL, ""))
        b_address = str(b_row.get(B_ADDRESS_COL, ""))
        b_village = extract_village_name(b_address)
        
        # 跳过多人或公司记录
        if has_multiple_people_or_company(b_row.get(B_NAME_COL, "")):
            stats['skipped_multi'] += 1
            continue
        
        if not b_name:
            stats['no_match'] += 1
            continue
        
        # 查找姓名匹配
        if b_name in a_name_index:
            candidates = [idx for idx in a_name_index[b_name] if idx not in matched_a_indices]
            
            if not candidates:
                stats['no_match'] += 1
                continue
            
            if len(candidates) == 1:
                # 唯一匹配
                a_idx = candidates[0]
                matches.append((a_idx, b_idx))
                matched_a_indices.add(a_idx)
                stats['exact_match'] += 1
                
                a_row = a_data[a_idx]
                match_details.append({
                    'type': '唯一姓名匹配',
                    'a_name': a_row.get(A_NAME_COL, ""),
                    'b_name': b_name,
                    'a_address': a_row.get(A_ADDRESS_COL, ""),
                    'b_address': b_address,
                    'confidence': 'high'
                })
                
            else:
                # 多个候选，使用村名筛选
                stats['multiple_candidates'] += 1
                
                best_match = None
                best_score = 0
                
                for a_idx in candidates:
                    a_row = a_data[a_idx]
                    a_address = str(a_row.get(A_ADDRESS_COL, ""))
                    a_village = extract_village_name(a_address)
                    
                    village_sim = similarity(b_village, a_village)
                    address_sim = similarity(b_address, a_address)
                    score = village_sim * 0.7 + address_sim * 0.3
                    
                    if score > best_score:
                        best_score = score
                        best_match = a_idx
                
                if best_match is not None and best_score >= 0.5:
                    matches.append((best_match, b_idx))
                    matched_a_indices.add(best_match)
                    stats['village_match'] += 1
                    
                    a_row = a_data[best_match]
                    match_details.append({
                        'type': f'村名辅助匹配(得分:{best_score:.2f})',
                        'a_name': a_row.get(A_NAME_COL, ""),
                        'b_name': b_name,
                        'a_address': a_row.get(A_ADDRESS_COL, ""),
                        'b_address': b_address,
                        'confidence': 'medium' if best_score >= 0.7 else 'low'
                    })
                else:
                    stats['no_match'] += 1
        else:
            stats['no_match'] += 1
    
    return matches, match_details, stats

def generate_final_result(a_data, b_data, b_headers, matches, match_details):
    """生成最终结果 - 使用B表列名结构"""
    print(f"\n生成最终合并结果...")
    
    # 列索引
    A_ID_COL = 3        # A表身份证号码
    A_ORIGINAL_AREA_COL = 7   # A表原合同面积
    A_CONFIRMED_AREA_COL = 8  # A表确权面积
    A_ADDRESS_COL = 13  # A表地址
    
    B_NAME_COL = 8      # B表权利人名称
    B_ID_COL = 9        # B表身份证号码
    B_ORIGINAL_AREA_COL = 10  # B表原合同面积
    B_CONFIRMED_AREA_COL = 11 # B表确权面积
    
    # 创建匹配映射
    match_map = {}
    for a_idx, b_idx in matches:
        match_map[b_idx] = a_idx
    
    # 生成结果数据
    result_data = []
    
    for b_idx, b_row in enumerate(b_data):
        # 创建结果行，使用B表的列名
        result_row = {}
        
        # 复制B表所有数据，使用实际列名
        for col_idx in range(len(b_headers)):
            col_name = b_headers.get(col_idx, f"列{col_idx}")
            result_row[col_name] = b_row.get(col_idx, "")
        
        # 如果有匹配，补充A表数据
        if b_idx in match_map:
            a_idx = match_map[b_idx]
            a_row = a_data[a_idx]
            
            # 补充身份证号码（如果B表为空）
            id_col_name = b_headers.get(B_ID_COL, "身份证号码")
            if not result_row.get(id_col_name, "").strip():
                result_row[id_col_name] = a_row.get(A_ID_COL, "")
            
            # 补充原合同面积（如果B表为空）
            original_area_col_name = b_headers.get(B_ORIGINAL_AREA_COL, "原合同面积（亩）")
            if not result_row.get(original_area_col_name, "").strip():
                result_row[original_area_col_name] = a_row.get(A_ORIGINAL_AREA_COL, "")
            
            # 补充确权面积（如果B表为空）
            confirmed_area_col_name = b_headers.get(B_CONFIRMED_AREA_COL, "确权（合同）面积（亩）")
            if not result_row.get(confirmed_area_col_name, "").strip():
                result_row[confirmed_area_col_name] = a_row.get(A_CONFIRMED_AREA_COL, "")
            
            # 添加匹配状态信息
            result_row['匹配状态'] = "已匹配"
            
            # 查找匹配类型
            match_type = "已匹配"
            for detail in match_details:
                if (detail['a_name'] == a_row.get(1, "") and 
                    detail['b_name'] == b_row.get(B_NAME_COL, "")):
                    match_type = detail['type']
                    break
            result_row['匹配类型'] = match_type
            result_row['A表地址'] = a_row.get(A_ADDRESS_COL, "")
            
        else:
            # 未匹配的记录
            if has_multiple_people_or_company(b_row.get(B_NAME_COL, "")):
                result_row['匹配状态'] = "跳过(多人/公司)"
            else:
                result_row['匹配状态'] = "未找到匹配"
            result_row['匹配类型'] = "无"
            result_row['A表地址'] = ""
        
        result_data.append(result_row)
    
    return result_data

def save_final_results(result_data, a_data, matches, match_details, stats):
    """保存最终结果"""
    print(f"\n保存结果文件...")
    
    # 保存主要合并结果
    with open('最终合并结果.csv', 'w', newline='', encoding='utf-8-sig') as f:
        if result_data:
            writer = csv.DictWriter(f, fieldnames=result_data[0].keys())
            writer.writeheader()
            writer.writerows(result_data)
    
    # 保存A表未匹配记录
    matched_a_indices = set(match[0] for match in matches)
    unmatched_a = [row for i, row in enumerate(a_data) if i not in matched_a_indices]
    
    if unmatched_a:
        with open('A表未匹配记录.csv', 'w', newline='', encoding='utf-8-sig') as f:
            if unmatched_a:
                fieldnames = ['承包方代码', '承包方代表名称', '户主性别', '承包方代表证件号码', '家庭成员数', 
                            '合同代码', '经营权证代码', '原合同面积（亩）', '确权（合同）面积（亩）', '承包地块数',
                            '发包方代码', '发包方名称', '承包方式', '地址', '承包经营权证流水号', '其他']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for row in unmatched_a:
                    csv_row = {}
                    for i, field in enumerate(fieldnames):
                        csv_row[field] = row.get(i, "")
                    writer.writerow(csv_row)
    
    print(f"文件保存完成:")
    print(f"- 最终合并结果.csv: {len(result_data)}行")
    print(f"- A表未匹配记录.csv: {len(unmatched_a)}行")

def generate_final_report(stats, matches, a_data, b_data):
    """生成最终报告"""
    print(f"\n" + "="*60)
    print("最终数据合并报告")
    print("="*60)
    
    print(f"1. 数据概况")
    print(f"   A表总记录数: {len(a_data)}")
    print(f"   B表总记录数: {len(b_data)}")
    
    print(f"\n2. B表处理结果")
    print(f"   总记录数: {stats['total_b']}")
    print(f"   跳过记录(多人/公司): {stats['skipped_multi']} ({stats['skipped_multi']/stats['total_b']*100:.1f}%)")
    print(f"   唯一姓名匹配: {stats['exact_match']}")
    print(f"   村名辅助匹配: {stats['village_match']}")
    print(f"   无法匹配: {stats['no_match']}")
    
    total_matched = stats['exact_match'] + stats['village_match']
    valid_records = stats['total_b'] - stats['skipped_multi']
    
    print(f"\n3. 匹配效果")
    print(f"   总匹配成功: {total_matched}/{valid_records} ({total_matched/valid_records*100:.1f}%)")
    print(f"   整体匹配率: {total_matched/stats['total_b']*100:.1f}%")
    
    print(f"\n4. 输出说明")
    print(f"   - 使用B表原有列名结构")
    print(f"   - 身份证号码、原合同面积、确权面积直接填入B表对应列")
    print(f"   - 新增：匹配状态、匹配类型、A表地址列")
    
    print(f"\n✅ 合并完成！请查看 '最终合并结果.csv' 文件")

def main():
    """主函数"""
    print("最终智能数据合并系统")
    print("策略：姓名优先匹配 + 村名辅助筛选")
    print("输出：统一使用B表列名结构")
    print("="*60)
    
    # 读取数据
    a_headers, a_data = read_excel_data('A.XLSX', header_row=4, data_start_row=5)
    if not a_data:
        return
    
    b_headers, b_data = read_excel_data('B.xlsx', header_row=1, data_start_row=2)
    if not b_data:
        return
    
    # 执行智能匹配
    matches, match_details, stats = smart_matching(a_data, b_data)
    
    # 生成最终结果
    result_data = generate_final_result(a_data, b_data, b_headers, matches, match_details)
    
    # 保存结果
    save_final_results(result_data, a_data, matches, match_details, stats)
    
    # 生成最终报告
    generate_final_report(stats, matches, a_data, b_data)

if __name__ == "__main__":
    main()
