#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能数据合并脚本 - 姓名优先，村名辅助匹配策略
"""

import zipfile
import re
import csv
from difflib import SequenceMatcher
from collections import defaultdict

def extract_shared_strings(filename):
    """提取共享字符串"""
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                strings = re.findall(r'<t[^>]*>([^<]*)</t>', content)
                return strings
    except Exception as e:
        print(f"提取共享字符串失败: {e}")
        return []

def parse_row_correct(row_xml, shared_strings):
    """正确解析行数据"""
    cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_xml)
    row_data = {}
    
    for cell_ref, cell_type, value in cells:
        col_letter = ''.join(filter(str.isalpha, cell_ref))
        col_index = sum((ord(c) - ord('A') + 1) * (26 ** i) for i, c in enumerate(reversed(col_letter))) - 1
        
        if value:
            try:
                idx = int(value)
                if 0 <= idx < len(shared_strings):
                    row_data[col_index] = shared_strings[idx]
                else:
                    row_data[col_index] = value
            except ValueError:
                row_data[col_index] = value
        else:
            row_data[col_index] = ""
    
    return row_data

def read_excel_data(filename, header_row, data_start_row):
    """读取Excel数据"""
    print(f"读取 {filename}...")
    
    shared_strings = extract_shared_strings(filename)
    print(f"共享字符串数量: {len(shared_strings)}")
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                # 提取表头
                headers = {}
                for row_num, row_content in rows:
                    if int(row_num) == header_row:
                        headers = parse_row_correct(row_content, shared_strings)
                        break
                
                # 提取所有数据
                data = []
                for row_num, row_content in rows:
                    if int(row_num) >= data_start_row:
                        row_data = parse_row_correct(row_content, shared_strings)
                        max_col = max(headers.keys()) if headers else 0
                        for i in range(max_col + 1):
                            if i not in row_data:
                                row_data[i] = ""
                        data.append(row_data)
                
                print(f"表头: {len(headers)}列, 数据: {len(data)}行")
                return headers, data
                
    except Exception as e:
        print(f"读取失败: {e}")
        return {}, []

def similarity(a, b):
    """计算字符串相似度"""
    if not a or not b:
        return 0
    return SequenceMatcher(None, str(a), str(b)).ratio()

def extract_village_name(address):
    """从地址中提取村名"""
    if not address:
        return ""
    
    address = str(address)
    # 更精确的村名提取
    patterns = [
        r'([^市县区镇乡街道]*村)(?:委员会)?',
        r'([^市县区镇乡街道]*社区)',
        r'([^市县区镇乡街道]*组)',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, address)
        if matches:
            return matches[-1]  # 取最后一个匹配（最具体的）
    
    # 如果没有找到村名，尝试提取镇名
    town_patterns = [
        r'([^市县区]*镇)',
        r'([^市县区]*乡)',
    ]
    
    for pattern in town_patterns:
        matches = re.findall(pattern, address)
        if matches:
            return matches[-1]
    
    return address

def clean_name(name):
    """清理姓名"""
    if not name:
        return ""
    return str(name).strip().replace(' ', '').replace('　', '')

def has_multiple_people_or_company(name):
    """检查是否为多人或公司"""
    if not name:
        return False
    
    name_str = str(name)
    # 检查多人标识
    if '、' in name_str or '，' in name_str or ',' in name_str:
        return True
    
    # 检查公司关键词
    company_keywords = ['公司', '企业', '集团', '有限', '股份', '合作社', '协会', '基金会', '中心', '组织']
    return any(keyword in name_str for keyword in company_keywords)

def smart_matching(a_data, b_data):
    """智能匹配策略：姓名优先，村名辅助"""
    print(f"\n执行智能匹配...")
    
    # 列索引
    A_NAME_COL = 1      # 承包方代表名称
    A_ADDRESS_COL = 13  # 地址
    A_ID_COL = 3        # 承包方代表证件号码
    A_ORIGINAL_AREA_COL = 7   # 原合同面积（亩）
    A_CONFIRMED_AREA_COL = 8  # 确权（合同）面积（亩）
    
    B_NAME_COL = 8      # 权利人名称
    B_ADDRESS_COL = 1   # 坐落
    B_ID_COL = 9        # 身份证号码
    
    # 第一步：按姓名建立索引
    print("第一步：建立姓名索引...")
    a_name_index = defaultdict(list)
    for a_idx, a_row in enumerate(a_data):
        name = clean_name(a_row.get(A_NAME_COL, ""))
        if name:
            a_name_index[name].append(a_idx)
    
    print(f"A表姓名索引：{len(a_name_index)}个不同姓名")
    
    # 统计重名情况
    duplicate_names = {name: indices for name, indices in a_name_index.items() if len(indices) > 1}
    print(f"A表重名情况：{len(duplicate_names)}个姓名有重复")
    
    # 第二步：处理B表记录
    print("\n第二步：匹配B表记录...")
    matches = []
    match_details = []
    matched_a_indices = set()
    
    # 统计信息
    stats = {
        'total_b': 0,
        'skipped_multi': 0,
        'exact_match': 0,
        'village_match': 0,
        'no_match': 0,
        'multiple_candidates': 0
    }
    
    for b_idx, b_row in enumerate(b_data):
        stats['total_b'] += 1
        
        b_name = clean_name(b_row.get(B_NAME_COL, ""))
        b_address = str(b_row.get(B_ADDRESS_COL, ""))
        b_village = extract_village_name(b_address)
        
        # 跳过多人或公司记录
        if has_multiple_people_or_company(b_row.get(B_NAME_COL, "")):
            stats['skipped_multi'] += 1
            continue
        
        if not b_name:
            stats['no_match'] += 1
            continue
        
        # 查找姓名匹配的A表记录
        if b_name in a_name_index:
            candidates = [idx for idx in a_name_index[b_name] if idx not in matched_a_indices]
            
            if not candidates:
                stats['no_match'] += 1
                continue
            
            if len(candidates) == 1:
                # 唯一匹配
                a_idx = candidates[0]
                matches.append((a_idx, b_idx))
                matched_a_indices.add(a_idx)
                stats['exact_match'] += 1
                
                a_row = a_data[a_idx]
                match_details.append({
                    'type': '唯一姓名匹配',
                    'a_name': a_row.get(A_NAME_COL, ""),
                    'b_name': b_name,
                    'a_address': a_row.get(A_ADDRESS_COL, ""),
                    'b_address': b_address,
                    'confidence': 'high'
                })
                
            else:
                # 多个候选，使用村名进一步筛选
                stats['multiple_candidates'] += 1
                print(f"  姓名 '{b_name}' 有{len(candidates)}个候选，使用村名筛选...")
                
                best_match = None
                best_score = 0
                
                for a_idx in candidates:
                    a_row = a_data[a_idx]
                    a_address = str(a_row.get(A_ADDRESS_COL, ""))
                    a_village = extract_village_name(a_address)
                    
                    # 计算村名相似度
                    village_sim = similarity(b_village, a_village)
                    address_sim = similarity(b_address, a_address)
                    
                    # 综合得分：村名相似度权重更高
                    score = village_sim * 0.7 + address_sim * 0.3
                    
                    print(f"    候选{a_idx}: {a_village} vs {b_village} (村名相似度: {village_sim:.2f}, 综合得分: {score:.2f})")
                    
                    if score > best_score:
                        best_score = score
                        best_match = a_idx
                
                # 如果最佳匹配的得分足够高，则匹配
                if best_match is not None and best_score >= 0.5:
                    matches.append((best_match, b_idx))
                    matched_a_indices.add(best_match)
                    stats['village_match'] += 1
                    
                    a_row = a_data[best_match]
                    match_details.append({
                        'type': f'村名辅助匹配(得分:{best_score:.2f})',
                        'a_name': a_row.get(A_NAME_COL, ""),
                        'b_name': b_name,
                        'a_address': a_row.get(A_ADDRESS_COL, ""),
                        'b_address': b_address,
                        'confidence': 'medium' if best_score >= 0.7 else 'low'
                    })
                    
                    print(f"    → 匹配成功: {extract_village_name(a_data[best_match].get(A_ADDRESS_COL, ''))} (得分: {best_score:.2f})")
                else:
                    stats['no_match'] += 1
                    print(f"    → 无合适匹配 (最高得分: {best_score:.2f})")
        else:
            stats['no_match'] += 1
    
    return matches, match_details, stats

def generate_merge_result(a_data, b_data, b_headers, matches, match_details):
    """生成合并结果 - 使用B表列名"""
    print(f"\n生成合并结果...")

    # 列索引
    A_NAME_COL = 1
    A_ADDRESS_COL = 13
    A_ID_COL = 3
    A_ORIGINAL_AREA_COL = 7
    A_CONFIRMED_AREA_COL = 8

    B_NAME_COL = 8
    B_ADDRESS_COL = 1
    B_ID_COL = 9           # B表身份证号码列
    B_ORIGINAL_AREA_COL = 10  # B表原合同面积列
    B_CONFIRMED_AREA_COL = 11 # B表确权面积列

    # 创建结果数据
    result_data = []

    for b_idx, b_row in enumerate(b_data):
        # 查找是否有匹配
        matched_a_idx = None
        match_type = "未匹配"

        for a_idx, matched_b_idx in matches:
            if matched_b_idx == b_idx:
                matched_a_idx = a_idx
                # 查找匹配类型
                for detail in match_details:
                    if (detail['a_name'] == a_data[a_idx].get(A_NAME_COL, "") and
                        detail['b_name'] == b_row.get(B_NAME_COL, "")):
                        match_type = detail['type']
                        break
                break

        # 构建结果行 - 使用B表的列名
        result_row = {}

        # 复制B表的所有列，使用实际的列名
        for col_idx in range(25):  # B表有25列
            header_name = b_headers.get(col_idx, f"列{col_idx}")
            result_row[header_name] = b_row.get(col_idx, "")

        # 如果有匹配，填入A表的数据到B表对应列中
        if matched_a_idx is not None:
            a_row = a_data[matched_a_idx]

            # 填入身份证号码（如果B表该列为空）
            if not result_row.get(b_headers.get(B_ID_COL, "身份证号码"), ""):
                result_row[b_headers.get(B_ID_COL, "身份证号码")] = a_row.get(A_ID_COL, "")

            # 填入原合同面积（如果B表该列为空）
            if not result_row.get(b_headers.get(B_ORIGINAL_AREA_COL, "原合同面积（亩）"), ""):
                result_row[b_headers.get(B_ORIGINAL_AREA_COL, "原合同面积（亩）")] = a_row.get(A_ORIGINAL_AREA_COL, "")

            # 填入确权面积（如果B表该列为空）
            if not result_row.get(b_headers.get(B_CONFIRMED_AREA_COL, "确权（合同）面积（亩）"), ""):
                result_row[b_headers.get(B_CONFIRMED_AREA_COL, "确权（合同）面积（亩）")] = a_row.get(A_CONFIRMED_AREA_COL, "")

            # 添加匹配状态信息（新增列）
            result_row['匹配状态'] = "已匹配"
            result_row['匹配类型'] = match_type
            result_row['A表地址'] = a_row.get(A_ADDRESS_COL, "")  # 保留A表地址用于对比
        else:
            # 判断未匹配原因
            if has_multiple_people_or_company(b_row.get(B_NAME_COL, "")):
                result_row['匹配状态'] = "跳过(多人/公司)"
            else:
                result_row['匹配状态'] = "未找到匹配"
            result_row['匹配类型'] = "无"
            result_row['A表地址'] = ""

        result_data.append(result_row)

    return result_data

def save_results(result_data, a_data, matches, match_details, stats):
    """保存结果到CSV文件"""
    print(f"\n保存结果...")
    
    # 保存主要合并结果
    with open('合并结果.csv', 'w', newline='', encoding='utf-8-sig') as f:
        if result_data:
            writer = csv.DictWriter(f, fieldnames=result_data[0].keys())
            writer.writeheader()
            writer.writerows(result_data)
    
    # 保存A表未匹配记录
    matched_a_indices = set(match[0] for match in matches)
    unmatched_a = [row for i, row in enumerate(a_data) if i not in matched_a_indices]
    
    if unmatched_a:
        with open('A表未匹配记录.csv', 'w', newline='', encoding='utf-8-sig') as f:
            if unmatched_a:
                # 使用A表的列索引作为字段名
                fieldnames = [f'A_{i}' for i in range(16)]  # A表有16列
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for row in unmatched_a:
                    csv_row = {f'A_{i}': row.get(i, "") for i in range(16)}
                    writer.writerow(csv_row)
    
    # 保存详细匹配报告
    with open('匹配详情报告.csv', 'w', newline='', encoding='utf-8-sig') as f:
        if match_details:
            writer = csv.DictWriter(f, fieldnames=match_details[0].keys())
            writer.writeheader()
            writer.writerows(match_details)

def generate_final_report(stats, matches, a_data, b_data):
    """生成最终报告"""
    print(f"\n" + "="*60)
    print("智能数据合并最终报告")
    print("="*60)
    
    print(f"1. 数据概况")
    print(f"   A表总记录数: {len(a_data)}")
    print(f"   B表总记录数: {len(b_data)}")
    
    print(f"\n2. B表处理结果")
    print(f"   总记录数: {stats['total_b']}")
    print(f"   跳过记录(多人/公司): {stats['skipped_multi']} ({stats['skipped_multi']/stats['total_b']*100:.1f}%)")
    print(f"   唯一姓名匹配: {stats['exact_match']}")
    print(f"   村名辅助匹配: {stats['village_match']}")
    print(f"   无法匹配: {stats['no_match']}")
    print(f"   多候选情况: {stats['multiple_candidates']}")
    
    total_matched = stats['exact_match'] + stats['village_match']
    valid_records = stats['total_b'] - stats['skipped_multi']
    
    print(f"\n3. 匹配效果")
    print(f"   总匹配成功: {total_matched}/{valid_records} ({total_matched/valid_records*100:.1f}%)")
    print(f"   匹配成功率: {total_matched/stats['total_b']*100:.1f}% (基于全部B表记录)")
    
    print(f"\n4. A表利用情况")
    matched_a_count = len(set(match[0] for match in matches))
    print(f"   已匹配A表记录: {matched_a_count}/{len(a_data)} ({matched_a_count/len(a_data)*100:.1f}%)")
    print(f"   未匹配A表记录: {len(a_data) - matched_a_count}")
    
    print(f"\n5. 输出文件")
    print(f"   - 合并结果.csv: 完整的合并结果")
    print(f"   - A表未匹配记录.csv: A表中未被匹配的记录")
    print(f"   - 匹配详情报告.csv: 详细的匹配过程记录")
    
    print(f"\n6. 建议")
    if total_matched/valid_records < 0.5:
        print(f"   - 匹配率较低，建议检查地址格式或降低匹配阈值")
    if stats['multiple_candidates'] > 0:
        print(f"   - 发现{stats['multiple_candidates']}个重名情况，村名辅助匹配发挥了作用")
    if stats['skipped_multi'] > stats['total_b'] * 0.3:
        print(f"   - 多人/公司记录较多，建议人工处理")

def main():
    """主函数"""
    print("智能数据合并系统")
    print("策略：姓名优先匹配 + 村名辅助筛选")
    print("="*60)
    
    # 读取数据
    a_headers, a_data = read_excel_data('A.XLSX', header_row=4, data_start_row=5)
    if not a_data:
        return
    
    b_headers, b_data = read_excel_data('B.xlsx', header_row=1, data_start_row=2)
    if not b_data:
        return
    
    # 执行智能匹配
    matches, match_details, stats = smart_matching(a_data, b_data)
    
    # 生成合并结果
    result_data = generate_merge_result(a_data, b_data, b_headers, matches, match_details)
    
    # 保存结果
    save_results(result_data, a_data, matches, match_details, stats)
    
    # 生成最终报告
    generate_final_report(stats, matches, a_data, b_data)

if __name__ == "__main__":
    main()
