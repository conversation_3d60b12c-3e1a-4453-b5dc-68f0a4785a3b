#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据合并脚本
将A表的数据合并到B表中，根据人名和村名匹配
"""

import pandas as pd
import numpy as np
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
import re
from difflib import SequenceMatcher

def similarity(a, b):
    """计算两个字符串的相似度"""
    return SequenceMatcher(None, a, b).ratio()

def extract_village_name(address):
    """从地址中提取村名"""
    if pd.isna(address):
        return ""
    
    # 常见的村名模式
    patterns = [
        r'(.+村)',
        r'(.+社区)',
        r'(.+镇)',
        r'(.+乡)',
        r'(.+街道)',
        r'(.+组)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, str(address))
        if match:
            return match.group(1)
    
    return str(address)

def clean_name(name):
    """清理姓名，去除空格和特殊字符"""
    if pd.isna(name):
        return ""
    return str(name).strip().replace(' ', '')

def has_multiple_people_or_company(name):
    """检查是否包含多人（顿号分隔）或公司名"""
    if pd.isna(name):
        return False
    
    name_str = str(name)
    # 检查是否有顿号分隔的多人
    if '、' in name_str:
        return True
    
    # 检查是否是公司名（包含常见公司关键词）
    company_keywords = ['公司', '企业', '集团', '有限', '股份', '合作社', '协会', '基金会', '中心']
    for keyword in company_keywords:
        if keyword in name_str:
            return True
    
    return False

def find_best_match(target_name, target_village, df_source, name_col, village_col):
    """在源数据中找到最佳匹配"""
    best_match_idx = None
    best_score = 0
    
    for idx, row in df_source.iterrows():
        source_name = clean_name(row[name_col])
        source_village = extract_village_name(row[village_col])
        
        # 姓名完全匹配
        name_match = (target_name == source_name)
        
        # 村名相似度匹配（阈值0.6）
        village_similarity = similarity(target_village, source_village)
        village_match = village_similarity >= 0.6
        
        # 计算综合得分
        if name_match and village_match:
            score = 1.0 + village_similarity  # 姓名完全匹配且村名匹配
        elif name_match:
            score = 0.8  # 仅姓名匹配
        elif village_match and similarity(target_name, source_name) >= 0.8:
            score = 0.6 + similarity(target_name, source_name) * 0.2  # 村名匹配且姓名相似
        else:
            score = 0
        
        if score > best_score:
            best_score = score
            best_match_idx = idx
    
    return best_match_idx if best_score >= 0.6 else None

def merge_data():
    """主要的数据合并函数"""
    print("开始读取Excel文件...")
    
    # 读取A表（从第4行开始）
    try:
        df_a = pd.read_excel('A.XLSX', header=3)
        print(f"A表读取成功，共{len(df_a)}行数据")
    except Exception as e:
        print(f"读取A表失败: {e}")
        return
    
    # 读取B表（从第1行开始）
    try:
        df_b = pd.read_excel('B.xlsx', header=0)
        print(f"B表读取成功，共{len(df_b)}行数据")
    except Exception as e:
        print(f"读取B表失败: {e}")
        return
    
    print("\nA表列名:")
    for i, col in enumerate(df_a.columns):
        print(f"{i}: {col}")
    
    print("\nB表列名:")
    for i, col in enumerate(df_b.columns):
        print(f"{i}: {col}")
    
    # 找到相关列
    # A表中的列
    a_name_col = None
    a_village_col = None
    a_id_col = None
    a_original_area_col = None
    a_confirmed_area_col = None
    
    for col in df_a.columns:
        if '承包方代表证件号码' in str(col):
            a_id_col = col
        elif '地址' in str(col):
            a_village_col = col
        elif '原合同面积' in str(col):
            a_original_area_col = col
        elif '确权' in str(col) and '面积' in str(col):
            a_confirmed_area_col = col
        elif '姓名' in str(col) or '承包方' in str(col):
            if a_name_col is None:  # 取第一个包含姓名的列
                a_name_col = col
    
    # B表中的列
    b_name_col = None
    b_village_col = None
    
    for col in df_b.columns:
        if '权利人名称' in str(col):
            b_name_col = col
        elif '坐落' in str(col):
            b_village_col = col
    
    print(f"\n识别的A表关键列:")
    print(f"姓名列: {a_name_col}")
    print(f"地址列: {a_village_col}")
    print(f"身份证列: {a_id_col}")
    print(f"原合同面积列: {a_original_area_col}")
    print(f"确权面积列: {a_confirmed_area_col}")
    
    print(f"\n识别的B表关键列:")
    print(f"权利人名称列: {b_name_col}")
    print(f"坐落列: {b_village_col}")
    
    if not all([a_name_col, a_village_col, b_name_col, b_village_col]):
        print("错误：无法找到所有必需的列")
        return
    
    # 创建结果DataFrame
    result_df = df_b.copy()
    
    # 添加新列
    result_df['身份证号码'] = ''
    result_df['原合同面积（亩）'] = ''
    result_df['确权（合同）面积（亩）'] = ''
    result_df['匹配状态'] = ''
    
    # 记录匹配情况
    matched_a_indices = set()
    b_skip_indices = set()
    
    print("\n开始匹配数据...")
    
    for b_idx, b_row in result_df.iterrows():
        b_name = clean_name(b_row[b_name_col])
        b_village = extract_village_name(b_row[b_village_col])
        
        # 检查B表中的权利人名称是否包含多人或公司名
        if has_multiple_people_or_company(b_row[b_name_col]):
            result_df.at[b_idx, '匹配状态'] = '多人或公司，跳过匹配'
            b_skip_indices.add(b_idx)
            continue
        
        # 在A表中查找匹配
        match_idx = find_best_match(b_name, b_village, df_a, a_name_col, a_village_col)
        
        if match_idx is not None:
            # 找到匹配，复制数据
            if a_id_col and not pd.isna(df_a.at[match_idx, a_id_col]):
                result_df.at[b_idx, '身份证号码'] = df_a.at[match_idx, a_id_col]
            
            if a_original_area_col and not pd.isna(df_a.at[match_idx, a_original_area_col]):
                result_df.at[b_idx, '原合同面积（亩）'] = df_a.at[match_idx, a_original_area_col]
            
            if a_confirmed_area_col and not pd.isna(df_a.at[match_idx, a_confirmed_area_col]):
                result_df.at[b_idx, '确权（合同）面积（亩）'] = df_a.at[match_idx, a_confirmed_area_col]
            
            result_df.at[b_idx, '匹配状态'] = '已匹配'
            matched_a_indices.add(match_idx)
        else:
            result_df.at[b_idx, '匹配状态'] = '未找到匹配'
    
    # 找出A表中未被匹配的记录
    unmatched_a_indices = set(range(len(df_a))) - matched_a_indices
    
    print(f"\n匹配结果统计:")
    print(f"B表总记录数: {len(df_b)}")
    print(f"B表成功匹配: {len([idx for idx in range(len(result_df)) if result_df.at[idx, '匹配状态'] == '已匹配'])}")
    print(f"B表多人/公司跳过: {len(b_skip_indices)}")
    print(f"B表未匹配: {len([idx for idx in range(len(result_df)) if result_df.at[idx, '匹配状态'] == '未找到匹配'])}")
    print(f"A表未被匹配记录数: {len(unmatched_a_indices)}")
    
    # 保存结果到Excel文件
    print("\n保存结果到Excel文件...")
    
    with pd.ExcelWriter('合并结果.xlsx', engine='openpyxl') as writer:
        result_df.to_excel(writer, sheet_name='合并结果', index=False)
        
        # 如果有未匹配的A表记录，也保存到另一个sheet
        if unmatched_a_indices:
            unmatched_df = df_a.iloc[list(unmatched_a_indices)].copy()
            unmatched_df.to_excel(writer, sheet_name='A表未匹配记录', index=False)
    
    # 添加颜色标记
    print("添加颜色标记...")
    
    wb = load_workbook('合并结果.xlsx')
    ws = wb['合并结果']
    
    # 定义颜色
    yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')
    
    # 为多人/公司和未匹配的行标黄
    for row_idx in range(2, len(result_df) + 2):  # Excel行号从2开始（第1行是表头）
        df_idx = row_idx - 2
        if df_idx in b_skip_indices or result_df.at[df_idx, '匹配状态'] == '未找到匹配':
            for col_idx in range(1, len(result_df.columns) + 1):
                ws.cell(row=row_idx, column=col_idx).fill = yellow_fill
    
    # 如果有A表未匹配记录的sheet，也标黄
    if 'A表未匹配记录' in wb.sheetnames:
        ws_unmatched = wb['A表未匹配记录']
        for row_idx in range(2, len(unmatched_a_indices) + 2):
            for col_idx in range(1, len(df_a.columns) + 1):
                ws_unmatched.cell(row=row_idx, column=col_idx).fill = yellow_fill
    
    wb.save('合并结果.xlsx')
    
    print(f"\n合并完成！结果已保存到 '合并结果.xlsx'")
    print("- 主表：合并结果")
    print("- 黄色标记：B表中多人/公司记录和未匹配记录")
    if unmatched_a_indices:
        print("- A表未匹配记录：单独保存在 'A表未匹配记录' sheet中，已标黄")

if __name__ == "__main__":
    merge_data()
