#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确调试脚本 - 确定正确的列映射和数据内容
"""

import zipfile
import re

def debug_precise_mapping(filename):
    """精确调试列映射"""
    print(f"\n精确调试 {filename}")
    print("="*60)
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            # 读取共享字符串
            shared_strings = []
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                shared_strings = re.findall(r'<t[^>]*>([^<]*)</t>', content)
            
            print(f"共享字符串数量: {len(shared_strings)}")
            
            # 读取工作表
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                # 确定表头行和数据行
                if filename.upper().endswith('A.XLSX'):
                    header_row = 4
                    data_rows = [5, 6, 7]
                    print("A表分析 - 表头第4行，数据第5-7行")
                else:
                    header_row = 1
                    data_rows = [2, 3, 4]
                    print("B表分析 - 表头第1行，数据第2-4行")
                
                # 分析表头行
                for row_num, row_content in rows:
                    if int(row_num) == header_row:
                        print(f"\n表头行({row_num})分析:")
                        
                        # 使用更精确的正则表达式
                        cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_content)
                        
                        headers = {}
                        for cell_ref, cell_type, value in cells:
                            col_letter = ''.join(filter(str.isalpha, cell_ref))
                            col_index = sum((ord(c) - ord('A') + 1) * (26 ** i) for i, c in enumerate(reversed(col_letter))) - 1
                            
                            if value:
                                if cell_type == 's':  # 共享字符串
                                    try:
                                        idx = int(value)
                                        if 0 <= idx < len(shared_strings):
                                            headers[col_index] = shared_strings[idx]
                                            print(f"  列{col_index}({cell_ref}): {shared_strings[idx]}")
                                        else:
                                            headers[col_index] = f"[索引{idx}超出范围]"
                                            print(f"  列{col_index}({cell_ref}): [索引{idx}超出范围]")
                                    except:
                                        headers[col_index] = value
                                        print(f"  列{col_index}({cell_ref}): {value} (解析失败)")
                                else:
                                    headers[col_index] = value
                                    print(f"  列{col_index}({cell_ref}): {value} (数值)")
                        break
                
                # 分析数据行
                for target_row in data_rows:
                    for row_num, row_content in rows:
                        if int(row_num) == target_row:
                            print(f"\n数据行({row_num})分析:")
                            
                            cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_content)
                            
                            row_data = {}
                            for cell_ref, cell_type, value in cells:
                                col_letter = ''.join(filter(str.isalpha, cell_ref))
                                col_index = sum((ord(c) - ord('A') + 1) * (26 ** i) for i, c in enumerate(reversed(col_letter))) - 1
                                
                                if value:
                                    if cell_type == 's':  # 共享字符串
                                        try:
                                            idx = int(value)
                                            if 0 <= idx < len(shared_strings):
                                                row_data[col_index] = shared_strings[idx]
                                            else:
                                                row_data[col_index] = f"[索引{idx}]"
                                        except:
                                            row_data[col_index] = value
                                    else:
                                        row_data[col_index] = value
                                else:
                                    row_data[col_index] = ""
                            
                            # 显示前15列的数据
                            for i in range(15):
                                if i in row_data:
                                    print(f"  列{i}: {row_data[i]}")
                                else:
                                    print(f"  列{i}: [空]")
                            break
                
                return headers
                
    except Exception as e:
        print(f"调试失败: {e}")
        return {}

def identify_key_columns():
    """识别关键列"""
    print("\n" + "="*60)
    print("关键列识别")
    print("="*60)
    
    # 调试A表
    a_headers = debug_precise_mapping('A.XLSX')
    
    # 调试B表
    b_headers = debug_precise_mapping('B.xlsx')
    
    # 识别关键列
    print("\n关键列映射:")
    
    print("A表关键列:")
    for col_idx, header in a_headers.items():
        if '承包方代表名称' in str(header):
            print(f"  姓名列: {col_idx} - {header}")
        elif '地址' in str(header):
            print(f"  地址列: {col_idx} - {header}")
        elif '承包方代表证件号码' in str(header):
            print(f"  身份证列: {col_idx} - {header}")
        elif '原合同面积' in str(header):
            print(f"  原合同面积列: {col_idx} - {header}")
        elif '确权' in str(header) and '面积' in str(header):
            print(f"  确权面积列: {col_idx} - {header}")
    
    print("B表关键列:")
    for col_idx, header in b_headers.items():
        if '权利人名称' in str(header):
            print(f"  姓名列: {col_idx} - {header}")
        elif '坐落' in str(header):
            print(f"  地址列: {col_idx} - {header}")
        elif '身份证号码' in str(header):
            print(f"  身份证列: {col_idx} - {header}")
        elif '原合同面积' in str(header):
            print(f"  原合同面积列: {col_idx} - {header}")
        elif '确权' in str(header) and '面积' in str(header):
            print(f"  确权面积列: {col_idx} - {header}")

def main():
    """主函数"""
    print("精确列映射调试")
    identify_key_columns()

if __name__ == "__main__":
    main()
