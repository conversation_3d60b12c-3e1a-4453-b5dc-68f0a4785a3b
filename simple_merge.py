#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版数据合并脚本 - 使用标准库
"""

import csv
import re
from difflib import SequenceMatcher

def similarity(a, b):
    """计算两个字符串的相似度"""
    return SequenceMatcher(None, a, b).ratio()

def extract_village_name(address):
    """从地址中提取村名"""
    if not address or address == '':
        return ""
    
    # 常见的村名模式
    patterns = [
        r'(.+村)',
        r'(.+社区)',
        r'(.+镇)',
        r'(.+乡)',
        r'(.+街道)',
        r'(.+组)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, str(address))
        if match:
            return match.group(1)
    
    return str(address)

def clean_name(name):
    """清理姓名，去除空格和特殊字符"""
    if not name or name == '':
        return ""
    return str(name).strip().replace(' ', '')

def has_multiple_people_or_company(name):
    """检查是否包含多人（顿号分隔）或公司名"""
    if not name or name == '':
        return False
    
    name_str = str(name)
    # 检查是否有顿号分隔的多人
    if '、' in name_str:
        return True
    
    # 检查是否是公司名（包含常见公司关键词）
    company_keywords = ['公司', '企业', '集团', '有限', '股份', '合作社', '协会', '基金会', '中心']
    for keyword in company_keywords:
        if keyword in name_str:
            return True
    
    return False

def read_excel_as_csv():
    """尝试读取Excel文件，如果失败则提示用户转换为CSV"""
    print("由于环境限制，请将Excel文件转换为CSV格式：")
    print("1. 打开A.XLSX，另存为A.csv（UTF-8编码）")
    print("2. 打开B.xlsx，另存为B.csv（UTF-8编码）")
    print("3. 确保A.csv从第4行开始有数据（删除前3行表头）")
    print("4. 确保B.csv从第1行开始有数据")
    print("\n转换完成后，重新运行此脚本")
    
    # 检查CSV文件是否存在
    try:
        with open('A.csv', 'r', encoding='utf-8') as f:
            pass
        with open('B.csv', 'r', encoding='utf-8') as f:
            pass
        return True
    except FileNotFoundError:
        return False

def analyze_csv_structure():
    """分析CSV文件结构"""
    print("分析文件结构...")
    
    # 分析A表
    print("\nA表结构分析:")
    try:
        with open('A.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)
            print("列名:")
            for i, col in enumerate(headers):
                print(f"{i}: {col}")
            
            # 读取几行数据作为示例
            print("\n前3行数据示例:")
            for i, row in enumerate(reader):
                if i >= 3:
                    break
                print(f"行{i+1}: {row[:5]}...")  # 只显示前5列
    except Exception as e:
        print(f"读取A.csv失败: {e}")
        return None, None
    
    # 分析B表
    print("\nB表结构分析:")
    try:
        with open('B.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)
            print("列名:")
            for i, col in enumerate(headers):
                print(f"{i}: {col}")
            
            # 读取几行数据作为示例
            print("\n前3行数据示例:")
            for i, row in enumerate(reader):
                if i >= 3:
                    break
                print(f"行{i+1}: {row[:5]}...")  # 只显示前5列
    except Exception as e:
        print(f"读取B.csv失败: {e}")
        return None, None
    
    return True, True

def manual_column_mapping():
    """手动指定列映射"""
    print("\n请根据上面的列名分析，输入对应的列索引（从0开始）:")
    
    # A表列映射
    print("\nA表列映射:")
    try:
        a_name_col = int(input("姓名列索引: "))
        a_village_col = int(input("地址列索引: "))
        a_id_col = int(input("身份证号码列索引: "))
        a_original_area_col = int(input("原合同面积列索引: "))
        a_confirmed_area_col = int(input("确权面积列索引: "))
    except ValueError:
        print("输入错误，请输入数字")
        return None
    
    # B表列映射
    print("\nB表列映射:")
    try:
        b_name_col = int(input("权利人名称列索引: "))
        b_village_col = int(input("坐落列索引: "))
    except ValueError:
        print("输入错误，请输入数字")
        return None
    
    return {
        'a_name': a_name_col,
        'a_village': a_village_col,
        'a_id': a_id_col,
        'a_original_area': a_original_area_col,
        'a_confirmed_area': a_confirmed_area_col,
        'b_name': b_name_col,
        'b_village': b_village_col
    }

def process_csv_data(col_mapping):
    """处理CSV数据"""
    print("\n开始处理数据...")
    
    # 读取A表数据
    a_data = []
    try:
        with open('A.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers_a = next(reader)
            for row in reader:
                if len(row) > max(col_mapping['a_name'], col_mapping['a_village'], 
                                col_mapping['a_id'], col_mapping['a_original_area'], 
                                col_mapping['a_confirmed_area']):
                    a_data.append(row)
    except Exception as e:
        print(f"读取A表数据失败: {e}")
        return
    
    # 读取B表数据
    b_data = []
    try:
        with open('B.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers_b = next(reader)
            for row in reader:
                if len(row) > max(col_mapping['b_name'], col_mapping['b_village']):
                    b_data.append(row)
    except Exception as e:
        print(f"读取B表数据失败: {e}")
        return
    
    print(f"A表数据: {len(a_data)}行")
    print(f"B表数据: {len(b_data)}行")
    
    # 开始匹配
    matched_a_indices = set()
    b_skip_indices = set()
    results = []
    
    for b_idx, b_row in enumerate(b_data):
        b_name = clean_name(b_row[col_mapping['b_name']] if col_mapping['b_name'] < len(b_row) else "")
        b_village = extract_village_name(b_row[col_mapping['b_village']] if col_mapping['b_village'] < len(b_row) else "")
        
        # 检查是否为多人或公司
        if has_multiple_people_or_company(b_row[col_mapping['b_name']] if col_mapping['b_name'] < len(b_row) else ""):
            result_row = b_row + ["", "", "", "多人或公司，跳过匹配"]
            results.append(result_row)
            b_skip_indices.add(b_idx)
            continue
        
        # 查找匹配
        best_match_idx = None
        best_score = 0
        
        for a_idx, a_row in enumerate(a_data):
            if a_idx in matched_a_indices:
                continue
                
            a_name = clean_name(a_row[col_mapping['a_name']] if col_mapping['a_name'] < len(a_row) else "")
            a_village = extract_village_name(a_row[col_mapping['a_village']] if col_mapping['a_village'] < len(a_row) else "")
            
            # 姓名完全匹配
            name_match = (b_name == a_name and b_name != "")
            
            # 村名相似度匹配
            village_similarity = similarity(b_village, a_village) if b_village and a_village else 0
            village_match = village_similarity >= 0.6
            
            # 计算综合得分
            if name_match and village_match:
                score = 1.0 + village_similarity
            elif name_match:
                score = 0.8
            elif village_match and similarity(b_name, a_name) >= 0.8:
                score = 0.6 + similarity(b_name, a_name) * 0.2
            else:
                score = 0
            
            if score > best_score:
                best_score = score
                best_match_idx = a_idx
        
        # 添加结果
        if best_match_idx is not None and best_score >= 0.6:
            a_row = a_data[best_match_idx]
            id_card = a_row[col_mapping['a_id']] if col_mapping['a_id'] < len(a_row) else ""
            original_area = a_row[col_mapping['a_original_area']] if col_mapping['a_original_area'] < len(a_row) else ""
            confirmed_area = a_row[col_mapping['a_confirmed_area']] if col_mapping['a_confirmed_area'] < len(a_row) else ""
            
            result_row = b_row + [id_card, original_area, confirmed_area, "已匹配"]
            results.append(result_row)
            matched_a_indices.add(best_match_idx)
        else:
            result_row = b_row + ["", "", "", "未找到匹配"]
            results.append(result_row)
    
    # 生成报告
    generate_simple_report(len(a_data), len(b_data), results, matched_a_indices, b_skip_indices, a_data, col_mapping)
    
    # 保存结果
    save_results(results, headers_b, a_data, matched_a_indices, col_mapping)

def generate_simple_report(a_count, b_count, results, matched_a_indices, b_skip_indices, a_data, col_mapping):
    """生成简单报告"""
    matched_count = len([r for r in results if r[-1] == "已匹配"])
    skip_count = len(b_skip_indices)
    unmatched_b_count = len([r for r in results if r[-1] == "未找到匹配"])
    unmatched_a_count = a_count - len(matched_a_indices)
    
    report = f"""
================================================================================
数据合并处理报告
================================================================================

1. 基本统计信息
----------------------------------------
A表总记录数: {a_count}
B表总记录数: {b_count}

2. B表处理结果
----------------------------------------
成功匹配: {matched_count} ({matched_count/b_count*100:.1f}%)
多人/公司跳过: {skip_count} ({skip_count/b_count*100:.1f}%)
未找到匹配: {unmatched_b_count} ({unmatched_b_count/b_count*100:.1f}%)

3. A表数据利用情况
----------------------------------------
已匹配记录: {len(matched_a_indices)} ({len(matched_a_indices)/a_count*100:.1f}%)
未匹配记录: {unmatched_a_count} ({unmatched_a_count/a_count*100:.1f}%)

4. 处理建议
----------------------------------------"""
    
    if unmatched_b_count > b_count * 0.2:
        report += "\n• B表未匹配率较高，建议检查姓名和地址格式是否一致"
    if unmatched_a_count > a_count * 0.3:
        report += "\n• A表利用率较低，可能存在数据重复或格式不一致问题"
    if skip_count > 0:
        report += "\n• 建议人工处理多人/公司记录"
    
    report += "\n\n================================================================================"
    
    print(report)
    
    # 保存报告
    with open('处理报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

def save_results(results, headers_b, a_data, matched_a_indices, col_mapping):
    """保存结果到CSV文件"""
    # 保存合并结果
    with open('合并结果.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        # 写入表头
        new_headers = headers_b + ['身份证号码', '原合同面积（亩）', '确权（合同）面积（亩）', '匹配状态']
        writer.writerow(new_headers)
        # 写入数据
        writer.writerows(results)
    
    # 保存A表未匹配记录
    unmatched_a_indices = set(range(len(a_data))) - matched_a_indices
    if unmatched_a_indices:
        with open('A表未匹配记录.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            # 这里简化处理，直接写入所有列
            for idx in sorted(unmatched_a_indices):
                writer.writerow(a_data[idx])
    
    print(f"\n处理完成！生成的文件:")
    print("- 合并结果.csv")
    print("- 处理报告.txt")
    if unmatched_a_indices:
        print("- A表未匹配记录.csv")

def main():
    """主函数"""
    print("数据合并工具（简化版）")
    print("=" * 50)
    
    # 检查CSV文件
    if not read_excel_as_csv():
        return
    
    # 分析文件结构
    if not analyze_csv_structure()[0]:
        return
    
    # 获取列映射
    col_mapping = manual_column_mapping()
    if not col_mapping:
        return
    
    # 处理数据
    process_csv_data(col_mapping)

if __name__ == "__main__":
    main()
