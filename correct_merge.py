#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的数据合并脚本 - 基于实际测试结果
"""

import zipfile
import re
import csv
from difflib import SequenceMatcher

def extract_shared_strings(filename):
    """提取共享字符串"""
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                strings = re.findall(r'<t[^>]*>([^<]*)</t>', content)
                return strings
    except Exception as e:
        print(f"提取共享字符串失败: {e}")
        return []

def parse_row_correct(row_xml, shared_strings):
    """正确解析行数据"""
    cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_xml)
    row_data = {}
    
    for cell_ref, cell_type, value in cells:
        col_letter = ''.join(filter(str.isalpha, cell_ref))
        col_index = sum((ord(c) - ord('A') + 1) * (26 ** i) for i, c in enumerate(reversed(col_letter))) - 1
        
        if value:
            # 大部分数据都是共享字符串，即使没有明确标记t="s"
            try:
                idx = int(value)
                if 0 <= idx < len(shared_strings):
                    row_data[col_index] = shared_strings[idx]
                else:
                    row_data[col_index] = value  # 如果索引超出范围，使用原值
            except ValueError:
                # 如果不是数字，直接使用原值
                row_data[col_index] = value
        else:
            row_data[col_index] = ""
    
    return row_data

def read_excel_correct(filename, header_row, data_start_row, max_rows=50):
    """正确读取Excel数据"""
    print(f"读取 {filename}...")
    
    shared_strings = extract_shared_strings(filename)
    print(f"共享字符串数量: {len(shared_strings)}")
    
    # 显示一些关键的共享字符串
    print("关键共享字符串:")
    key_strings = ['承包方代表名称', '地址', '权利人名称', '坐落', '身份证号码', '承包方代表证件号码']
    for i, s in enumerate(shared_strings):
        if any(key in s for key in key_strings):
            print(f"  {i}: {s}")
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                # 提取表头
                headers = {}
                for row_num, row_content in rows:
                    if int(row_num) == header_row:
                        headers = parse_row_correct(row_content, shared_strings)
                        break
                
                # 提取数据
                data = []
                count = 0
                for row_num, row_content in rows:
                    if int(row_num) >= data_start_row and count < max_rows:
                        row_data = parse_row_correct(row_content, shared_strings)
                        # 确保所有列都有值
                        max_col = max(headers.keys()) if headers else 0
                        for i in range(max_col + 1):
                            if i not in row_data:
                                row_data[i] = ""
                        data.append(row_data)
                        count += 1
                
                print(f"表头: {len(headers)}列, 数据: {len(data)}行")
                return headers, data
                
    except Exception as e:
        print(f"读取失败: {e}")
        return {}, []

def show_data_samples(headers, data, title):
    """显示数据样本"""
    print(f"\n{title}:")
    print("表头:")
    for i in range(min(15, len(headers))):
        print(f"  {i}: {headers.get(i, '')}")
    
    print("数据样本:")
    for i, row in enumerate(data[:3]):
        print(f"  行{i+1}:")
        for j in range(min(10, len(headers))):
            print(f"    {j}: {row.get(j, '')}")

def find_key_columns(headers, data, table_name):
    """查找关键列"""
    print(f"\n{table_name}关键列识别:")
    
    key_columns = {}
    
    for col_idx, header in headers.items():
        header_str = str(header).strip()
        
        if table_name == "A表":
            if '承包方代表名称' in header_str:
                key_columns['name'] = col_idx
                print(f"  姓名列: {col_idx} - {header_str}")
            elif '地址' in header_str:
                key_columns['address'] = col_idx
                print(f"  地址列: {col_idx} - {header_str}")
            elif '承包方代表证件号码' in header_str:
                key_columns['id'] = col_idx
                print(f"  身份证列: {col_idx} - {header_str}")
            elif '原合同面积' in header_str:
                key_columns['original_area'] = col_idx
                print(f"  原合同面积列: {col_idx} - {header_str}")
            elif '确权' in header_str and '面积' in header_str:
                key_columns['confirmed_area'] = col_idx
                print(f"  确权面积列: {col_idx} - {header_str}")
        
        elif table_name == "B表":
            if '权利人名称' in header_str:
                key_columns['name'] = col_idx
                print(f"  姓名列: {col_idx} - {header_str}")
            elif '坐落' in header_str:
                key_columns['address'] = col_idx
                print(f"  地址列: {col_idx} - {header_str}")
            elif '身份证号码' in header_str:
                key_columns['id'] = col_idx
                print(f"  身份证列: {col_idx} - {header_str}")
            elif '原合同面积' in header_str:
                key_columns['original_area'] = col_idx
                print(f"  原合同面积列: {col_idx} - {header_str}")
            elif '确权' in header_str and '面积' in header_str:
                key_columns['confirmed_area'] = col_idx
                print(f"  确权面积列: {col_idx} - {header_str}")
    
    return key_columns

def similarity(a, b):
    """计算字符串相似度"""
    if not a or not b:
        return 0
    return SequenceMatcher(None, str(a), str(b)).ratio()

def extract_village_name(address):
    """从地址中提取村名"""
    if not address:
        return ""
    
    address = str(address)
    patterns = [
        r'([^市县区镇乡]*村)',
        r'([^市县区镇乡]*社区)',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, address)
        if matches:
            return matches[-1]  # 取最后一个匹配
    
    return address

def clean_name(name):
    """清理姓名"""
    if not name:
        return ""
    return str(name).strip().replace(' ', '')

def has_multiple_people_or_company(name):
    """检查是否为多人或公司"""
    if not name:
        return False
    
    name_str = str(name)
    if '、' in name_str or '，' in name_str:
        return True
    
    company_keywords = ['公司', '企业', '集团', '有限', '股份', '合作社', '协会', '基金会', '中心']
    return any(keyword in name_str for keyword in company_keywords)

def perform_matching(a_data, b_data, a_cols, b_cols):
    """执行匹配"""
    print(f"\n执行数据匹配...")
    
    if 'name' not in a_cols or 'name' not in b_cols:
        print("缺少姓名列，无法进行匹配")
        return []
    
    matches = []
    matched_a = set()
    
    # 策略1: 姓名+身份证精确匹配
    if 'id' in a_cols and 'id' in b_cols:
        print("策略1: 姓名+身份证精确匹配")
        for b_idx, b_row in enumerate(b_data):
            if has_multiple_people_or_company(b_row.get(b_cols['name'], "")):
                continue
            
            b_name = clean_name(b_row.get(b_cols['name'], ""))
            b_id = str(b_row.get(b_cols['id'], "")).strip()
            
            if not b_name or not b_id:
                continue
            
            for a_idx, a_row in enumerate(a_data):
                if a_idx in matched_a:
                    continue
                
                a_name = clean_name(a_row.get(a_cols['name'], ""))
                a_id = str(a_row.get(a_cols['id'], "")).strip()
                
                if b_name == a_name and b_id == a_id:
                    matches.append((a_idx, b_idx, "姓名+身份证"))
                    matched_a.add(a_idx)
                    print(f"  匹配: {b_name} | {b_id}")
                    break
    
    # 策略2: 姓名+地址相似匹配
    if 'address' in a_cols and 'address' in b_cols:
        print("策略2: 姓名+地址相似匹配")
        matched_b = set(m[1] for m in matches)
        
        for b_idx, b_row in enumerate(b_data):
            if b_idx in matched_b or has_multiple_people_or_company(b_row.get(b_cols['name'], "")):
                continue
            
            b_name = clean_name(b_row.get(b_cols['name'], ""))
            b_address = str(b_row.get(b_cols['address'], ""))
            b_village = extract_village_name(b_address)
            
            if not b_name:
                continue
            
            best_match = None
            best_score = 0
            
            for a_idx, a_row in enumerate(a_data):
                if a_idx in matched_a:
                    continue
                
                a_name = clean_name(a_row.get(a_cols['name'], ""))
                a_address = str(a_row.get(a_cols['address'], ""))
                a_village = extract_village_name(a_address)
                
                if b_name == a_name:
                    village_sim = similarity(b_village, a_village)
                    address_sim = similarity(b_address, a_address)
                    score = max(village_sim, address_sim)
                    
                    if score > best_score and score >= 0.5:
                        best_score = score
                        best_match = a_idx
            
            if best_match is not None:
                matches.append((best_match, b_idx, f"姓名+地址(相似度:{best_score:.2f})"))
                matched_a.add(best_match)
                print(f"  匹配: {b_name} | {b_village} vs {extract_village_name(a_data[best_match].get(a_cols['address'], ''))}")
    
    return matches

def generate_report(a_headers, b_headers, a_data, b_data, matches, a_cols, b_cols):
    """生成报告"""
    print(f"\n" + "="*60)
    print("数据合并测试报告")
    print("="*60)
    
    print(f"1. 数据概况")
    print(f"   A表: {len(a_data)}行测试数据")
    print(f"   B表: {len(b_data)}行测试数据")
    
    valid_b = [b for b in b_data if not has_multiple_people_or_company(b.get(b_cols.get('name', 0), ""))]
    skip_b = len(b_data) - len(valid_b)
    
    print(f"\n2. B表处理情况")
    print(f"   可处理记录: {len(valid_b)}")
    print(f"   跳过记录(多人/公司): {skip_b}")
    
    print(f"\n3. 匹配结果")
    print(f"   成功匹配: {len(matches)}/{len(valid_b)} ({len(matches)/len(valid_b)*100:.1f}%)")
    
    # 按策略分组
    strategy_counts = {}
    for _, _, strategy in matches:
        strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
    
    for strategy, count in strategy_counts.items():
        print(f"   {strategy}: {count}条")
    
    print(f"\n4. 匹配示例")
    for i, (a_idx, b_idx, strategy) in enumerate(matches[:5]):
        a_row = a_data[a_idx]
        b_row = b_data[b_idx]
        a_name = a_row.get(a_cols.get('name', 0), "")
        b_name = b_row.get(b_cols.get('name', 0), "")
        print(f"   {i+1}. A表: {a_name} | B表: {b_name} | 策略: {strategy}")
    
    print(f"\n5. 全量数据预估")
    if len(valid_b) > 0:
        match_rate = len(matches) / len(valid_b)
        print(f"   预计B表匹配率: {match_rate*100:.1f}%")
        print(f"   预计可匹配记录: {int(432 * match_rate)}条")
        print(f"   预计需人工处理: {int(432 * skip_b/len(b_data))}条")

def main():
    """主函数"""
    print("正确的数据合并测试")
    print("="*60)
    
    # 读取A表
    a_headers, a_data = read_excel_correct('A.XLSX', header_row=4, data_start_row=5)
    if not a_data:
        return
    
    # 读取B表
    b_headers, b_data = read_excel_correct('B.xlsx', header_row=1, data_start_row=2)
    if not b_data:
        return
    
    # 显示数据样本
    show_data_samples(a_headers, a_data, "A表")
    show_data_samples(b_headers, b_data, "B表")
    
    # 查找关键列
    a_cols = find_key_columns(a_headers, a_data, "A表")
    b_cols = find_key_columns(b_headers, b_data, "B表")
    
    # 执行匹配
    matches = perform_matching(a_data, b_data, a_cols, b_cols)
    
    # 生成报告
    generate_report(a_headers, b_headers, a_data, b_data, matches, a_cols, b_cols)

if __name__ == "__main__":
    main()
