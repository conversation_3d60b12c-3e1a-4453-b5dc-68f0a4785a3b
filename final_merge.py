#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终数据合并脚本 - 修复解析问题
"""

import zipfile
import re
import csv
from difflib import SequenceMatcher
from collections import defaultdict

def extract_shared_strings(filename):
    """提取共享字符串"""
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                strings = re.findall(r'<t[^>]*>([^<]*)</t>', content)
                return strings
    except Exception as e:
        print(f"提取共享字符串失败: {e}")
        return []

def parse_row_data_fixed(row_xml, shared_strings):
    """修复的行数据解析"""
    # 修正正则表达式，正确识别 t="s" 属性
    cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="s")?[^>]*>(?:<v>([^<]*)</v>)?', row_xml)
    row_data = {}
    
    for cell_ref, value in cells:
        col_letter = ''.join(filter(str.isalpha, cell_ref))
        col_index = sum((ord(c) - ord('A') + 1) * (26 ** i) for i, c in enumerate(reversed(col_letter))) - 1
        
        if value:
            # 检查是否是共享字符串（通过检查原始XML中是否有t="s"）
            if f'r="{cell_ref}"' in row_xml and 't="s"' in row_xml[row_xml.find(f'r="{cell_ref}"'):row_xml.find(f'r="{cell_ref}"')+100]:
                try:
                    idx = int(value)
                    if 0 <= idx < len(shared_strings):
                        row_data[col_index] = shared_strings[idx]
                    else:
                        row_data[col_index] = f"[索引{idx}]"
                except:
                    row_data[col_index] = value
            else:
                row_data[col_index] = value
        else:
            row_data[col_index] = ""
    
    return row_data

def read_excel_data_fixed(filename, header_row, data_start_row):
    """修复的Excel数据读取"""
    print(f"读取 {filename}...")
    
    shared_strings = extract_shared_strings(filename)
    print(f"共享字符串数量: {len(shared_strings)}")
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                # 提取表头
                headers = {}
                for row_num, row_content in rows:
                    if int(row_num) == header_row:
                        headers = parse_row_data_fixed(row_content, shared_strings)
                        break
                
                # 提取数据（只读取前100行进行测试）
                data = []
                count = 0
                for row_num, row_content in rows:
                    if int(row_num) >= data_start_row:
                        if count >= 100:  # 限制测试数据量
                            break
                        row_data = parse_row_data_fixed(row_content, shared_strings)
                        # 确保所有列都有值
                        max_col = max(headers.keys()) if headers else 0
                        for i in range(max_col + 1):
                            if i not in row_data:
                                row_data[i] = ""
                        data.append(row_data)
                        count += 1
                
                print(f"表头: {len(headers)}列, 测试数据: {len(data)}行")
                return headers, data
                
    except Exception as e:
        print(f"读取失败: {e}")
        return {}, []

def similarity(a, b):
    """计算字符串相似度"""
    if not a or not b:
        return 0
    return SequenceMatcher(None, str(a), str(b)).ratio()

def extract_village_name(address):
    """从地址中提取村名"""
    if not address:
        return ""
    
    address = str(address)
    patterns = [
        r'([^市县区镇乡]*村)',
        r'([^市县区镇乡]*社区)',
        r'([^市县区镇乡]*镇)',
        r'([^市县区镇乡]*乡)',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, address)
        if matches:
            return matches[-1]  # 取最后一个匹配（最具体的）
    
    return address

def clean_name(name):
    """清理姓名"""
    if not name:
        return ""
    return str(name).strip().replace(' ', '')

def has_multiple_people_or_company(name):
    """检查是否为多人或公司"""
    if not name:
        return False
    
    name_str = str(name)
    # 检查多人标识
    if '、' in name_str or '，' in name_str:
        return True
    
    # 检查公司关键词
    company_keywords = ['公司', '企业', '集团', '有限', '股份', '合作社', '协会', '基金会', '中心']
    return any(keyword in name_str for keyword in company_keywords)

def test_data_quality(a_data, b_data):
    """测试数据质量"""
    print("\n" + "="*60)
    print("数据质量测试")
    print("="*60)
    
    # A表列索引
    A_NAME_COL = 4      # 承包方代表名称
    A_ADDRESS_COL = 16  # 地址
    A_ID_COL = 6        # 承包方代表证件号码
    
    # B表列索引
    B_NAME_COL = 8      # 权利人名称
    B_ADDRESS_COL = 1   # 坐落
    B_ID_COL = 9        # 身份证号码
    
    print("A表数据样本:")
    for i, row in enumerate(a_data[:5]):
        name = row.get(A_NAME_COL, "")
        address = row.get(A_ADDRESS_COL, "")
        id_card = row.get(A_ID_COL, "")
        print(f"  {i+1}. 姓名: {name} | 地址: {address} | 身份证: {id_card}")
    
    print("\nB表数据样本:")
    for i, row in enumerate(b_data[:5]):
        name = row.get(B_NAME_COL, "")
        address = row.get(B_ADDRESS_COL, "")
        id_card = row.get(B_ID_COL, "")
        print(f"  {i+1}. 姓名: {name} | 地址: {address} | 身份证: {id_card}")
    
    # 统计数据质量
    a_empty_names = sum(1 for row in a_data if not row.get(A_NAME_COL, ""))
    a_empty_addresses = sum(1 for row in a_data if not row.get(A_ADDRESS_COL, ""))
    
    b_empty_names = sum(1 for row in b_data if not row.get(B_NAME_COL, ""))
    b_empty_addresses = sum(1 for row in b_data if not row.get(B_ADDRESS_COL, ""))
    
    print(f"\n数据质量统计:")
    print(f"A表空姓名: {a_empty_names}/{len(a_data)} ({a_empty_names/len(a_data)*100:.1f}%)")
    print(f"A表空地址: {a_empty_addresses}/{len(a_data)} ({a_empty_addresses/len(a_data)*100:.1f}%)")
    print(f"B表空姓名: {b_empty_names}/{len(b_data)} ({b_empty_names/len(b_data)*100:.1f}%)")
    print(f"B表空地址: {b_empty_addresses}/{len(b_data)} ({b_empty_addresses/len(b_data)*100:.1f}%)")

def perform_matching_test(a_data, b_data):
    """执行匹配测试"""
    print("\n" + "="*60)
    print("匹配测试")
    print("="*60)
    
    # 列索引
    A_NAME_COL = 4
    A_ADDRESS_COL = 16
    A_ID_COL = 6
    B_NAME_COL = 8
    B_ADDRESS_COL = 1
    B_ID_COL = 9
    
    # 策略1: 姓名+身份证精确匹配
    print("\n策略1: 姓名+身份证精确匹配")
    matches_1 = []
    for b_idx, b_row in enumerate(b_data):
        if has_multiple_people_or_company(b_row.get(B_NAME_COL, "")):
            continue
        
        b_name = clean_name(b_row.get(B_NAME_COL, ""))
        b_id = str(b_row.get(B_ID_COL, "")).strip()
        
        for a_idx, a_row in enumerate(a_data):
            a_name = clean_name(a_row.get(A_NAME_COL, ""))
            a_id = str(a_row.get(A_ID_COL, "")).strip()
            
            if b_name == a_name and b_id == a_id and b_name and b_id:
                matches_1.append((a_idx, b_idx))
                print(f"  匹配: {b_name} | {b_id}")
                break
    
    print(f"策略1匹配数: {len(matches_1)}")
    
    # 策略2: 姓名精确+村名相似
    print("\n策略2: 姓名精确+村名相似")
    matches_2 = []
    matched_b = set(m[1] for m in matches_1)
    
    for b_idx, b_row in enumerate(b_data):
        if b_idx in matched_b or has_multiple_people_or_company(b_row.get(B_NAME_COL, "")):
            continue
        
        b_name = clean_name(b_row.get(B_NAME_COL, ""))
        b_village = extract_village_name(b_row.get(B_ADDRESS_COL, ""))
        
        for a_idx, a_row in enumerate(a_data):
            a_name = clean_name(a_row.get(A_NAME_COL, ""))
            a_village = extract_village_name(a_row.get(A_ADDRESS_COL, ""))
            
            if (b_name == a_name and b_name and 
                similarity(b_village, a_village) >= 0.6):
                matches_2.append((a_idx, b_idx))
                print(f"  匹配: {b_name} | {b_village} vs {a_village} (相似度: {similarity(b_village, a_village):.2f})")
                break
    
    print(f"策略2新增匹配数: {len(matches_2)}")
    
    total_matches = len(matches_1) + len(matches_2)
    valid_b_count = len([b for b in b_data if not has_multiple_people_or_company(b.get(B_NAME_COL, ""))])
    
    print(f"\n总匹配结果:")
    print(f"总匹配数: {total_matches}/{valid_b_count} ({total_matches/valid_b_count*100:.1f}%)")
    
    return matches_1 + matches_2

def generate_final_report(a_headers, b_headers, a_data, b_data, matches):
    """生成最终报告"""
    print("\n" + "="*60)
    print("最终合并测试报告")
    print("="*60)
    
    print(f"1. 数据概况")
    print(f"   A表: {len(a_data)}行测试数据 (实际约6915行)")
    print(f"   B表: {len(b_data)}行测试数据 (实际约432行)")
    
    valid_b_count = len([b for b in b_data if not has_multiple_people_or_company(b.get(8, ""))])
    skip_b_count = len(b_data) - valid_b_count
    
    print(f"\n2. B表处理情况")
    print(f"   可处理记录: {valid_b_count}")
    print(f"   跳过记录(多人/公司): {skip_b_count}")
    
    print(f"\n3. 匹配结果")
    print(f"   成功匹配: {len(matches)}/{valid_b_count} ({len(matches)/valid_b_count*100:.1f}%)")
    print(f"   未匹配: {valid_b_count - len(matches)}")
    
    print(f"\n4. 推荐策略")
    print(f"   使用组合策略: 姓名+身份证精确匹配 + 姓名+村名相似匹配")
    print(f"   预期全量数据匹配率: 约{len(matches)/valid_b_count*100:.1f}%")
    
    print(f"\n5. 实施建议")
    print(f"   - 可以直接处理约{len(matches)/len(b_data)*432:.0f}条B表记录")
    print(f"   - 约{skip_b_count/len(b_data)*432:.0f}条多人/公司记录需人工处理")
    print(f"   - 约{(valid_b_count-len(matches))/len(b_data)*432:.0f}条记录可能需要降低匹配标准")

def main():
    """主函数"""
    print("数据合并最终测试")
    print("="*60)
    
    # 读取数据
    a_headers, a_data = read_excel_data_fixed('A.XLSX', header_row=4, data_start_row=5)
    if not a_data:
        print("无法读取A表数据")
        return
    
    b_headers, b_data = read_excel_data_fixed('B.xlsx', header_row=1, data_start_row=2)
    if not b_data:
        print("无法读取B表数据")
        return
    
    # 显示表头
    print(f"\nA表表头:")
    for i in range(min(17, len(a_headers))):
        print(f"  {i}: {a_headers.get(i, '')}")
    
    print(f"\nB表表头:")
    for i in range(min(12, len(b_headers))):
        print(f"  {i}: {b_headers.get(i, '')}")
    
    # 测试数据质量
    test_data_quality(a_data, b_data)
    
    # 执行匹配测试
    matches = perform_matching_test(a_data, b_data)
    
    # 生成最终报告
    generate_final_report(a_headers, b_headers, a_data, b_data, matches)

if __name__ == "__main__":
    main()
