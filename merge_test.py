#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据合并测试脚本 - 基于实际列索引
"""

import zipfile
import re
import csv
from difflib import SequenceMatcher
from collections import defaultdict

def extract_shared_strings(filename):
    """提取共享字符串"""
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                strings = re.findall(r'<t[^>]*>([^<]*)</t>', content)
                return strings
    except Exception as e:
        print(f"提取共享字符串失败: {e}")
        return []

def parse_row_data(row_xml, shared_strings):
    """解析行数据"""
    cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_xml)
    row_data = {}
    
    for cell_ref, cell_type, value in cells:
        col_letter = ''.join(filter(str.isalpha, cell_ref))
        col_index = sum((ord(c) - ord('A') + 1) * (26 ** i) for i, c in enumerate(reversed(col_letter))) - 1
        
        if cell_type == 's' and value:  # 共享字符串
            try:
                idx = int(value)
                if 0 <= idx < len(shared_strings):
                    row_data[col_index] = shared_strings[idx]
                else:
                    row_data[col_index] = f"[索引{idx}]"
            except:
                row_data[col_index] = value
        elif value:
            row_data[col_index] = value
        else:
            row_data[col_index] = ""
    
    return row_data

def read_excel_data(filename, header_row, data_start_row):
    """读取Excel数据"""
    print(f"读取 {filename}...")
    
    shared_strings = extract_shared_strings(filename)
    print(f"共享字符串数量: {len(shared_strings)}")
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                # 提取表头
                headers = {}
                for row_num, row_content in rows:
                    if int(row_num) == header_row:
                        headers = parse_row_data(row_content, shared_strings)
                        break
                
                # 提取数据
                data = []
                for row_num, row_content in rows:
                    if int(row_num) >= data_start_row:
                        row_data = parse_row_data(row_content, shared_strings)
                        # 确保所有列都有值
                        max_col = max(headers.keys()) if headers else 0
                        for i in range(max_col + 1):
                            if i not in row_data:
                                row_data[i] = ""
                        data.append(row_data)
                
                print(f"表头: {len(headers)}列, 数据: {len(data)}行")
                return headers, data
                
    except Exception as e:
        print(f"读取失败: {e}")
        return {}, []

def similarity(a, b):
    """计算字符串相似度"""
    if not a or not b:
        return 0
    return SequenceMatcher(None, str(a), str(b)).ratio()

def extract_village_name(address):
    """从地址中提取村名"""
    if not address:
        return ""
    
    address = str(address)
    patterns = [
        r'([^市县区镇乡]*村)',
        r'([^市县区镇乡]*社区)',
        r'([^市县区镇乡]*镇)',
        r'([^市县区镇乡]*乡)',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, address)
        if matches:
            return matches[-1]  # 取最后一个匹配（最具体的）
    
    return address

def clean_name(name):
    """清理姓名"""
    if not name:
        return ""
    return str(name).strip().replace(' ', '')

def has_multiple_people_or_company(name):
    """检查是否为多人或公司"""
    if not name:
        return False
    
    name_str = str(name)
    # 检查多人标识
    if '、' in name_str or '，' in name_str:
        return True
    
    # 检查公司关键词
    company_keywords = ['公司', '企业', '集团', '有限', '股份', '合作社', '协会', '基金会', '中心']
    return any(keyword in name_str for keyword in company_keywords)

def test_matching_strategies(a_data, b_data):
    """测试不同的匹配策略"""
    print("\n" + "="*60)
    print("测试不同匹配策略")
    print("="*60)
    
    # A表列索引（基于之前的分析）
    A_NAME_COL = 4      # 承包方代表名称
    A_ADDRESS_COL = 16  # 地址
    A_ID_COL = 6        # 承包方代表证件号码
    A_ORIGINAL_AREA_COL = 10  # 原合同面积（亩）
    A_CONFIRMED_AREA_COL = 11 # 确权（合同）面积（亩）
    
    # B表列索引
    B_NAME_COL = 8      # 权利人名称
    B_ADDRESS_COL = 1   # 坐落
    B_ID_COL = 9        # 身份证号码
    
    strategies = {
        "策略1: 姓名+身份证精确匹配": lambda a, b: (
            clean_name(a.get(A_NAME_COL, "")) == clean_name(b.get(B_NAME_COL, "")) and
            str(a.get(A_ID_COL, "")) == str(b.get(B_ID_COL, "")) and
            clean_name(a.get(A_NAME_COL, "")) != "" and
            str(a.get(A_ID_COL, "")) != ""
        ),
        
        "策略2: 姓名精确+村名匹配": lambda a, b: (
            clean_name(a.get(A_NAME_COL, "")) == clean_name(b.get(B_NAME_COL, "")) and
            similarity(extract_village_name(a.get(A_ADDRESS_COL, "")), 
                      extract_village_name(b.get(B_ADDRESS_COL, ""))) >= 0.6 and
            clean_name(a.get(A_NAME_COL, "")) != ""
        ),
        
        "策略3: 姓名精确+地址相似": lambda a, b: (
            clean_name(a.get(A_NAME_COL, "")) == clean_name(b.get(B_NAME_COL, "")) and
            similarity(str(a.get(A_ADDRESS_COL, "")), str(b.get(B_ADDRESS_COL, ""))) >= 0.4 and
            clean_name(a.get(A_NAME_COL, "")) != ""
        ),
        
        "策略4: 仅姓名精确匹配": lambda a, b: (
            clean_name(a.get(A_NAME_COL, "")) == clean_name(b.get(B_NAME_COL, "")) and
            clean_name(a.get(A_NAME_COL, "")) != ""
        ),
    }
    
    results = {}
    
    for strategy_name, match_func in strategies.items():
        print(f"\n测试 {strategy_name}:")
        
        matched_pairs = []
        matched_a_indices = set()
        matched_b_indices = set()
        
        for b_idx, b_row in enumerate(b_data):
            # 跳过多人或公司记录
            if has_multiple_people_or_company(b_row.get(B_NAME_COL, "")):
                continue
            
            best_match = None
            best_score = 0
            
            for a_idx, a_row in enumerate(a_data):
                if a_idx in matched_a_indices:
                    continue
                
                if match_func(a_row, b_row):
                    # 计算综合得分
                    name_sim = similarity(clean_name(a_row.get(A_NAME_COL, "")), 
                                        clean_name(b_row.get(B_NAME_COL, "")))
                    addr_sim = similarity(str(a_row.get(A_ADDRESS_COL, "")), 
                                        str(b_row.get(B_ADDRESS_COL, "")))
                    score = name_sim * 0.7 + addr_sim * 0.3
                    
                    if score > best_score:
                        best_score = score
                        best_match = a_idx
            
            if best_match is not None:
                matched_pairs.append((best_match, b_idx, best_score))
                matched_a_indices.add(best_match)
                matched_b_indices.add(b_idx)
        
        # 统计结果
        total_b = len([b for b in b_data if not has_multiple_people_or_company(b.get(B_NAME_COL, ""))])
        match_count = len(matched_pairs)
        match_rate = match_count / total_b * 100 if total_b > 0 else 0
        
        print(f"  匹配成功: {match_count}/{total_b} ({match_rate:.1f}%)")
        print(f"  A表利用率: {len(matched_a_indices)}/{len(a_data)} ({len(matched_a_indices)/len(a_data)*100:.1f}%)")
        
        # 显示一些匹配示例
        print("  匹配示例:")
        for i, (a_idx, b_idx, score) in enumerate(matched_pairs[:3]):
            a_row = a_data[a_idx]
            b_row = b_data[b_idx]
            print(f"    {i+1}. A表: {a_row.get(A_NAME_COL, '')} | {extract_village_name(a_row.get(A_ADDRESS_COL, ''))}")
            print(f"       B表: {b_row.get(B_NAME_COL, '')} | {extract_village_name(b_row.get(B_ADDRESS_COL, ''))} (得分: {score:.3f})")
        
        results[strategy_name] = {
            'matched_pairs': matched_pairs,
            'match_count': match_count,
            'total_b': total_b,
            'match_rate': match_rate,
            'a_utilization': len(matched_a_indices)/len(a_data)*100
        }
    
    return results

def analyze_unmatched_data(a_data, b_data, best_strategy_pairs):
    """分析未匹配的数据"""
    print("\n" + "="*60)
    print("未匹配数据分析")
    print("="*60)
    
    A_NAME_COL = 4
    A_ADDRESS_COL = 16
    B_NAME_COL = 8
    B_ADDRESS_COL = 1
    
    matched_b_indices = {pair[1] for pair in best_strategy_pairs}
    
    # 分析B表未匹配记录
    unmatched_b = []
    skipped_b = []
    
    for b_idx, b_row in enumerate(b_data):
        if has_multiple_people_or_company(b_row.get(B_NAME_COL, "")):
            skipped_b.append((b_idx, b_row))
        elif b_idx not in matched_b_indices:
            unmatched_b.append((b_idx, b_row))
    
    print(f"B表未匹配分析:")
    print(f"  多人/公司记录: {len(skipped_b)}")
    print(f"  无法匹配记录: {len(unmatched_b)}")
    
    if skipped_b:
        print(f"\n  多人/公司记录示例:")
        for i, (idx, row) in enumerate(skipped_b[:5]):
            print(f"    {i+1}. {row.get(B_NAME_COL, '')} | {extract_village_name(row.get(B_ADDRESS_COL, ''))}")
    
    if unmatched_b:
        print(f"\n  无法匹配记录示例:")
        for i, (idx, row) in enumerate(unmatched_b[:5]):
            print(f"    {i+1}. {row.get(B_NAME_COL, '')} | {extract_village_name(row.get(B_ADDRESS_COL, ''))}")
    
    return unmatched_b, skipped_b

def generate_test_report(a_headers, b_headers, a_data, b_data, strategy_results):
    """生成测试报告"""
    print("\n" + "="*60)
    print("数据合并测试报告")
    print("="*60)
    
    print(f"\n1. 数据概况")
    print(f"   A表: {len(a_data)}行数据")
    print(f"   B表: {len(b_data)}行数据")
    
    print(f"\n2. 策略测试结果对比")
    print(f"{'策略':<25} {'匹配率':<10} {'A表利用率':<12} {'匹配数量':<10}")
    print("-" * 60)
    
    best_strategy = None
    best_score = 0
    
    for strategy_name, result in strategy_results.items():
        # 综合评分：匹配率权重0.7，A表利用率权重0.3
        composite_score = result['match_rate'] * 0.7 + result['a_utilization'] * 0.3
        
        print(f"{strategy_name:<25} {result['match_rate']:<10.1f}% {result['a_utilization']:<12.1f}% {result['match_count']:<10}")
        
        if composite_score > best_score:
            best_score = composite_score
            best_strategy = strategy_name
    
    print(f"\n3. 推荐策略: {best_strategy}")
    print(f"   综合得分: {best_score:.1f}")
    
    # 分析最佳策略的未匹配数据
    best_pairs = strategy_results[best_strategy]['matched_pairs']
    unmatched_b, skipped_b = analyze_unmatched_data(a_data, b_data, best_pairs)
    
    print(f"\n4. 处理建议")
    print(f"   - 使用推荐策略可匹配 {strategy_results[best_strategy]['match_count']} 条记录")
    print(f"   - {len(skipped_b)} 条多人/公司记录需要人工处理")
    print(f"   - {len(unmatched_b)} 条记录可能需要降低匹配标准或人工核实")
    
    return best_strategy, best_pairs

def main():
    """主函数"""
    print("数据合并策略测试")
    print("="*60)
    
    # 读取A表数据
    a_headers, a_data = read_excel_data('A.XLSX', header_row=4, data_start_row=5)
    if not a_data:
        print("无法读取A表数据")
        return
    
    # 读取B表数据
    b_headers, b_data = read_excel_data('B.xlsx', header_row=1, data_start_row=2)
    if not b_data:
        print("无法读取B表数据")
        return
    
    # 显示表头信息
    print(f"\nA表表头预览:")
    for i in range(min(17, len(a_headers))):
        print(f"  {i}: {a_headers.get(i, '')}")
    
    print(f"\nB表表头预览:")
    for i in range(min(12, len(b_headers))):
        print(f"  {i}: {b_headers.get(i, '')}")
    
    # 测试匹配策略
    strategy_results = test_matching_strategies(a_data, b_data)
    
    # 生成报告
    best_strategy, best_pairs = generate_test_report(a_headers, b_headers, a_data, b_data, strategy_results)
    
    print(f"\n测试完成！推荐使用: {best_strategy}")

if __name__ == "__main__":
    main()
