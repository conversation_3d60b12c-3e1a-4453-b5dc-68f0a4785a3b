#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据内容
"""

import zipfile
import re

def debug_excel_content(filename):
    """调试Excel文件内容"""
    print(f"\n调试 {filename}")
    print("="*50)
    
    try:
        with zipfile.ZipFile(filename, 'r') as zip_file:
            # 读取共享字符串
            shared_strings = []
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                shared_strings = re.findall(r'<t[^>]*>([^<]*)</t>', content)
            
            print(f"共享字符串数量: {len(shared_strings)}")
            print("前20个共享字符串:")
            for i, s in enumerate(shared_strings[:20]):
                print(f"  {i}: {s}")
            
            # 读取工作表
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                
                # 查找特定行
                if filename.upper().endswith('A.XLSX'):
                    target_rows = [4, 5]  # 表头和第一行数据
                else:
                    target_rows = [1, 2]  # 表头和第一行数据
                
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                for target_row in target_rows:
                    for row_num, row_content in rows:
                        if int(row_num) == target_row:
                            print(f"\n第{row_num}行原始内容:")
                            print(row_content[:500] + "...")
                            
                            print(f"\n第{row_num}行解析结果:")
                            cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_content)
                            
                            for cell_ref, cell_type, value in cells[:10]:  # 只显示前10个单元格
                                if cell_type == 's' and value:
                                    try:
                                        idx = int(value)
                                        if 0 <= idx < len(shared_strings):
                                            actual_value = shared_strings[idx]
                                            print(f"  {cell_ref}: {actual_value} (索引{idx})")
                                        else:
                                            print(f"  {cell_ref}: [索引{idx}超出范围]")
                                    except:
                                        print(f"  {cell_ref}: {value} (解析失败)")
                                elif value:
                                    print(f"  {cell_ref}: {value} (数值)")
                                else:
                                    print(f"  {cell_ref}: [空]")
                            break
    
    except Exception as e:
        print(f"调试失败: {e}")

def test_name_matching():
    """测试姓名匹配"""
    print("\n" + "="*50)
    print("测试姓名匹配")
    print("="*50)
    
    # 从A表获取一些姓名样本
    try:
        with zipfile.ZipFile('A.XLSX', 'r') as zip_file:
            shared_strings_a = []
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                shared_strings_a = re.findall(r'<t[^>]*>([^<]*)</t>', content)
            
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                print("A表前5行数据的姓名列(第4列):")
                for row_num, row_content in rows[4:9]:  # 第5-9行数据
                    cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_content)
                    
                    # 查找第4列（E列）
                    for cell_ref, cell_type, value in cells:
                        if cell_ref.startswith('E'):
                            if cell_type == 's' and value:
                                try:
                                    idx = int(value)
                                    if 0 <= idx < len(shared_strings_a):
                                        print(f"  行{row_num}: {shared_strings_a[idx]}")
                                    else:
                                        print(f"  行{row_num}: [索引{idx}超出范围]")
                                except:
                                    print(f"  行{row_num}: {value}")
                            break
    
    except Exception as e:
        print(f"A表测试失败: {e}")
    
    # 从B表获取一些姓名样本
    try:
        with zipfile.ZipFile('B.xlsx', 'r') as zip_file:
            shared_strings_b = []
            with zip_file.open('xl/sharedStrings.xml') as f:
                content = f.read().decode('utf-8')
                shared_strings_b = re.findall(r'<t[^>]*>([^<]*)</t>', content)
            
            with zip_file.open('xl/worksheets/sheet1.xml') as f:
                content = f.read().decode('utf-8')
                rows = re.findall(r'<row[^>]*r="(\d+)"[^>]*>(.*?)</row>', content, re.DOTALL)
                
                print("\nB表前5行数据的姓名列(第8列):")
                for row_num, row_content in rows[1:6]:  # 第2-6行数据
                    cells = re.findall(r'<c[^>]*r="([^"]*)"[^>]*(?:t="([^"]*)")?[^>]*>(?:<v>([^<]*)</v>)?', row_content)
                    
                    # 查找第8列（I列）
                    for cell_ref, cell_type, value in cells:
                        if cell_ref.startswith('I'):
                            if cell_type == 's' and value:
                                try:
                                    idx = int(value)
                                    if 0 <= idx < len(shared_strings_b):
                                        print(f"  行{row_num}: {shared_strings_b[idx]}")
                                    else:
                                        print(f"  行{row_num}: [索引{idx}超出范围]")
                                except:
                                    print(f"  行{row_num}: {value}")
                            break
    
    except Exception as e:
        print(f"B表测试失败: {e}")

def check_column_mapping():
    """检查列映射是否正确"""
    print("\n" + "="*50)
    print("检查列映射")
    print("="*50)
    
    # 根据之前看到的共享字符串，确认列索引
    a_columns = {
        3: "承包方代码",
        4: "承包方代表名称", 
        5: "户主性别",
        6: "承包方代表证件号码",
        7: "家庭成员数",
        8: "合同代码",
        9: "经营权证代码", 
        10: "原合同面积（亩）",
        11: "确权（合同）面积（亩）",
        12: "承包地块数",
        13: "发包方代码",
        14: "发包方名称",
        15: "承包方式",
        16: "地址"
    }
    
    b_columns = {
        0: "业务号",
        1: "坐落",
        2: "不动产证号", 
        3: "证书类型",
        4: "使用期限",
        5: "起始日期",
        6: "结束日期",
        7: "登记面积",
        8: "权利人名称",
        9: "身份证号码",
        10: "原合同面积（亩）",
        11: "确权（合同）面积（亩）"
    }
    
    print("A表列映射:")
    for idx, name in a_columns.items():
        print(f"  {idx}: {name}")
    
    print("\nB表列映射:")
    for idx, name in b_columns.items():
        print(f"  {idx}: {name}")
    
    print("\n关键匹配列:")
    print(f"  姓名匹配: A表第{4}列 vs B表第{8}列")
    print(f"  地址匹配: A表第{16}列 vs B表第{1}列") 
    print(f"  身份证匹配: A表第{6}列 vs B表第{9}列")

def main():
    """主函数"""
    print("数据内容调试")
    
    # 调试A表
    debug_excel_content('A.XLSX')
    
    # 调试B表  
    debug_excel_content('B.xlsx')
    
    # 测试姓名匹配
    test_name_matching()
    
    # 检查列映射
    check_column_mapping()

if __name__ == "__main__":
    main()
